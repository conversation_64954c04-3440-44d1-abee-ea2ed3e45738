import { AsyncLocalStorage } from 'node:async_hooks';

export type CoreServiceScene = 'web' | 'koncall' | 'openapi' | 'agent';

export type CompletionAsyncLocalStorageType = {
  traceId: string;
  originIp: string;
  appId: string;
  conversationId: string;
  username: string;
  scene: CoreServiceScene;
  /** 用于保证每次请求的日志顺序 */
  seqId: number;
};

export const CompletionAsyncLocalStorage = new AsyncLocalStorage<CompletionAsyncLocalStorageType>();
