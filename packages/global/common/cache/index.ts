import { LRUCache } from 'lru-cache';

const TOTAL_CACHE_SIZE = 1024 * 1024 * 500; // 500MB
const MaxCacheSize = {
  Files: 1024 * 1024 * 200 // 200MB
} as const;

const MAX_CACHE_SIZE = Object.values(MaxCacheSize).reduce((acc, cur) => acc + cur, 0);
if (MAX_CACHE_SIZE > TOTAL_CACHE_SIZE) {
  throw new Error(
    `Cache size exceeds the limit, total: ${TOTAL_CACHE_SIZE}, current: ${MAX_CACHE_SIZE}`
  );
}

export type CachedFileResult = {
  rawText: string;
  pages?: Array<{
    pageNumber: number;
    content: string;
  }>;
};

export const FilesCache = new LRUCache<string, CachedFileResult>({
  max: 100,
  maxSize: MaxCacheSize.Files,
  updateAgeOnGet: true,
  sizeCalculation: (value, key) => {
    const pagesLength = value.pages?.reduce((acc, cur) => acc + cur.content.length, 0) || 0;
    return value.rawText.length + pagesLength + key.length;
  },
  // 5min
  ttl: 1000 * 60 * 5
});

export const RpcCacheKeys = {
  ListModel: 'ListModel'
} as const;

// 缓存一些 rpc 请求的数据
export const RpcCache = new LRUCache<string, string>({
  max: 2000,
  allowStale: false,
  updateAgeOnGet: false,
  updateAgeOnHas: false,
  ttl: 1000 * 60 * 3
});
