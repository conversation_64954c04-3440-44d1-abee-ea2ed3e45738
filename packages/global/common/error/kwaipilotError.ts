import { FlowNodeTypeEnum } from '../../core/workflow/node/constant';
// 定义错误类型枚举及其对应信息
export const ErrorTypeEnum = {
  INTERNAL_ERROR: {
    code: '1000',
    msg: '内部错误'
  },
  CLAUDE_QUOTA_EXCEED: {
    code: '1001',
    msg: 'A<PERSON> Claude 超限'
  },

  FILE_READ_ERROR: {
    code: '1002',
    msg: '文件读取失败'
  },

  MODEL_PARAM_ERROR: {
    code: '1004',
    msg: 'kwaipilot 模型输入参数错误'
  },
  MODEL_32K_OVERFLOW: {
    code: '1005',
    msg: '模型请求出错'
  },

  BLOB_IMAGE_ERROR: {
    code: '1006',
    msg: '从 blob 获取图片异常'
  },

  RAG_CODE_TIMEOUT: {
    code: '1007',
    msg: 'Rag Code 请求超时'
  },
  RAG_WEB_TIMEOUT: {
    code: '1008',
    msg: 'RAG Web 服务访问超时'
  },

  OPENAI_REQUEST_ERROR: {
    code: '1009',
    msg: 'Azure openai 服务请求报错'
  },

  GPT_CONTENT_ERROR: {
    code: '1010',
    msg: 'GPT 模型命中敏感提示词'
  },
  // kwaipilot工具执行失败
  KWAIPILOT_TOOL_ERROR: {
    code: '1011',
    msg: 'kwaipilot工具执行失败'
  },
  CLAUDE_REQUEST_ERROR: {
    code: '1012',
    msg: 'Claude 服务请求报错'
  },
  CONTEXT_EXCEED_ERROR: {
    code: '1013',
    msg: '上下文超出'
  },
  OPENAI_REQUEST_TIMEOUT: {
    code: '1014',
    msg: 'Azure openai 服务请求超时'
  },
  DEVELOP_REQUEST_ERROR: {
    code: '1015',
    msg: '研发能力请求报错'
  },
  CODE_INTERPRETER_ERROR: {
    code: '1016',
    msg: '代码执行器执行失败'
  },
  RAG_REQUEST_ERROR: {
    code: '1017',
    msg: 'RAG 请求报错'
  }
} as const;

type SLATypeEnumKey = 'observation' | 'modelError' | 'engineError';

// 类型及对应的 code
const SLATypeMap: Record<SLATypeEnumKey, string> = {
  // 只观测，不计入 SLA
  observation: '100',
  // 计入 SLA, 模型侧异常
  modelError: '200',
  // 计入 SLA, 工程侧异常
  engineError: '300'
};

type ErrorTypeEnumKey = keyof typeof ErrorTypeEnum;

export class KError extends Error {
  code: string | number;
  msg: string;
  detail?: Record<string, any>;

  constructor(type: ErrorTypeEnumKey, slaType: SLATypeEnumKey, detail?: Record<string, any>) {
    const errorInfo = ErrorTypeEnum[type];

    super(errorInfo.msg);

    // 设置错误名称
    this.name = 'KError';

    // 实际 code 由 节点 code + 错误类型 code 拼接而成
    const code = SLATypeMap[slaType] + errorInfo.code;

    Object.setPrototypeOf(this, KError.prototype);

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, KError);
    }

    // 设置自定义属性
    this.code = code;
    this.msg = errorInfo.msg;
    this.detail = detail;
  }
}

// 默认错误类型
export const defaultErrorTypeEnum = {
  code: SLATypeMap.engineError + ErrorTypeEnum.INTERNAL_ERROR.code,
  msg: ErrorTypeEnum.INTERNAL_ERROR.msg
};
