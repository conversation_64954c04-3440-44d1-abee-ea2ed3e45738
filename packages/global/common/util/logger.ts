import { Logger } from '@infra-node/logger';
import RotateFile from 'winston-daily-rotate-file';
import * as winston from 'winston';
import * as util from 'node:util';
import { join } from 'node:path';
import { IS_LOCAL, IS_KWS_PREONLINE, IS_PROD } from '../../core/chat/constants';
import { CompletionAsyncLocalStorage, type CoreServiceScene } from '../async-local-storage';

const logPath = join(process.cwd(), '../../logs');
const APP_NAME = process.env.KWS_SERVICE_NAME || 'kwaipilot-agents-engine';

const getSeqId = () => {
  const store = CompletionAsyncLocalStorage.getStore();
  if (!store) return 0;

  store.seqId = (store.seqId || 0) + 1;
  return store.seqId;
};

interface CommonTimeCostLogRequest {
  /**
   * 开始时间戳(ms)
   * @format int64
   */
  beginTimestamp?: number;
  /**
   * 耗时(ms)
   * @format int64
   */
  duration?: number;
  /**
   * 结束时间戳(ms)
   * @format int64
   */
  endTimestamp?: number;
  exception?: string;
  extra?: string;
  extra1?: string;
  extra2?: string;
  extra3?: string;
  extra4?: string;
  extra5?: string;
  extra6?: string;
  /** 模型类型 */
  modelType?: string;
  /**
   * 命名空间
   * @example "chat/code"
   */
  namespace: string;
  /**
   * 平台
   * @example "web"
   */
  platform: string;
  /** 请求ID */
  requestId?: string;
  /** 会话ID */
  sessionId?: string;
  /** 采集的阶段 */
  stage?: string;
  /** 用户名 */
  username?: string;
  /** 版本 */
  version?: string;
}
const hiveLogCommonData: CommonTimeCostLogRequest = {
  namespace: 'agents-engine',
  platform: 'kwaipilot-web',
  version: '0.0.1',
  modelType: ''
};

const corePerfLogger = new Logger({
  appName: APP_NAME,
  isConsole: false,
  isFile: true,
  isTcp: false,
  namespace: 'kwaipilot.core.service',
  fileOptions: {
    dirname: join(process.cwd(), '../../legacy-logs'),
    zippedArchive: false,
    maxSize: '20m',
    createSymlink: true,
    maxFiles: '14d'
  }
});

export type CoreServiceSubTag =
  | 'chat-gpt4o'
  | 'chat-claude'
  | 'chat-kwaipilot-32k'
  | 'chat-kwaipilot-128k'
  | 'chat-kwaipilot-r1-math'
  | 'rag-knowledge-proxy'
  | 'rag-code-proxy'
  | 'rag-web-proxy'
  | 'rag-http-web'
  | 'rag-http-code'
  | 'rag-http-knowledge';

export const coreServicePerf = (data: {
  subtag: CoreServiceSubTag;
  ok: boolean;
  startTime: number;
}) => {
  const { subtag, startTime } = data;
  const store = CompletionAsyncLocalStorage.getStore();
  const scene = store?.scene || '';
  corePerfLogger.perf({
    subtag,
    millis: +new Date() - startTime,
    extra1: 'workflow',
    extra2: data.ok ? 'ok' : 'error',
    extra3: scene
  });
};

export const perLogger = new Logger({
  appName: APP_NAME,
  isConsole: false,
  isFile: true,
  isTcp: false,
  namespace: 'node.perf.kwaipilot-agents-engine',
  fileOptions: {
    dirname: join(process.cwd(), '../../legacy-logs'),
    zippedArchive: false,
    maxSize: '20m',
    createSymlink: true,
    maxFiles: '14d'
  }
});

const filterOnly = (level: string) => {
  return winston.format((info, opts) => {
    if (info.level === level) {
      return info;
    }
    return false;
  })();
};

const createRotateFile = (level: string) =>
  new RotateFile({
    dirname: logPath,
    filename: `${level}.log`,
    symlinkName: `${level}.log`,
    zippedArchive: false,
    maxSize: '2024m',
    maxFiles: '14d',
    level: level,
    createSymlink: true,
    format: filterOnly(level)
  });

const logTransports = [];
if (IS_LOCAL) {
  logTransports.push(new winston.transports.Console());
} else {
  logTransports.push(
    createRotateFile('info'),
    createRotateFile('warn'),
    createRotateFile('error'),
    createRotateFile('debug')
  );
}

const winstonLogger = winston.createLogger({
  format: IS_LOCAL ? winston.format.simple() : winston.format.json(),
  defaultMeta: { service: APP_NAME },
  transports: logTransports,
  exceptionHandlers: [new winston.transports.File({ filename: 'logs/exceptions.log' })]
});

const createLogFunction = (level: string, moduleType: string) => {
  return (
    message: unknown | unknown[],
    additions?: any,
    meta?: {
      appId: string;
      username: string;
      traceId: string;
      conversationId: string;
      scene?: CoreServiceScene;
    }
  ) => {
    let error = '';
    if (additions && additions.error) {
      error = additions.error.stack || additions.error.toString();
    }

    let msg = util.format(message);
    if (Array.isArray(message)) {
      msg = util.format(message[0], ...message.slice(1));
    }

    const store = CompletionAsyncLocalStorage.getStore();
    const requestId = meta?.conversationId || store?.conversationId;
    const userId = meta?.username || store?.username;
    const traceId = meta?.traceId || store?.traceId;
    const appId = meta?.appId || store?.appId;
    const scene = meta?.scene || store?.scene;
    const seqId = getSeqId();
    winstonLogger.log(level, `[${moduleType}] ${msg}`, {
      ...additions,
      level,
      time: +new Date(),
      service: APP_NAME,
      localtime: new Date().toLocaleString(),
      userId,
      traceId,
      requestId,
      appId,
      scene,
      moduleType,
      seqId,
      error
    });
  };
};

export const createLogger = (moduleType: string) => {
  return {
    info: createLogFunction('info', moduleType),
    warn: createLogFunction('warn', moduleType),
    error: createLogFunction('error', moduleType)
  };
};

/** 上报日志到 hive 表 */
export const collectLog2Hive = (data: Partial<CommonTimeCostLogRequest>) => {
  // 如果不是线上或预发，不进行上报
  if (!IS_PROD && !IS_KWS_PREONLINE) {
    return;
  }

  try {
    const costReportData: CommonTimeCostLogRequest = {
      ...hiveLogCommonData,
      ...data
    };
    fetch('http://kwaipilot-server.internal/eapi/kwaipilot/log/time', {
      method: 'POST',
      headers: {
        'Content-type': 'application/json'
      },
      body: JSON.stringify(costReportData)
    });
  } catch (error) {
    console.error('上报日志失败，错误原因：', error);
  }
};

export type RagDurationLogMode = 'web' | 'code' | 'kn' | 'file';
export type RagDurationLogStage =
  | 'workflow-rag-standalone-query'
  | 'workflow-rag-query'
  | 'workflow-rag-search'
  | 'workflow-rag-crawl'
  | 'workflow-rag-intention'
  | 'workflow-rag-result'
  | 'workflow-rag-total';

interface RagDurationLog {
  sessionId: string;
  beginTimestamp: number;
  duration: number;
  stage: RagDurationLogStage;
  username: string;
  mode: RagDurationLogMode;
  intent: string;
}
/** 上报Rag执行耗时 */
export const collectRagDurationLog = (data: RagDurationLog) => {
  // 上报 perflog
  perLogger.perf({
    subtag: 'rag.duration',
    millis: data.duration,
    // 节点类型
    extra1: data.stage,
    extra2: data.mode,
    extra3: data.intent
  });

  const store = CompletionAsyncLocalStorage.getStore();
  const traceId = store?.traceId;
  // 上报到 hive
  collectLog2Hive({
    namespace: 'workflow-rag',
    platform: 'kwaipilot-agent',
    username: data.username,
    requestId: traceId || '',
    sessionId: data.sessionId,
    version: '',
    beginTimestamp: data.beginTimestamp,
    duration: data.duration,
    stage: data.stage,
    extra1: data.mode,
    extra2: data.intent
  });
};
interface NodeDurationLog extends Partial<CommonTimeCostLogRequest> {
  /** 节点类型 */
  appId: string;
  nodeType: string;
  duration: number;
  username: string;
  modelType?: string;
  sessionId: string;
}
/** 上报节点执行耗时 */
export const collectNodeDurationLog = (data: NodeDurationLog) => {
  // 上报 perflog
  perLogger.perf({
    subtag: 'node.duration',
    millis: data.duration,
    // 节点类型
    extra1: data.nodeType,
    // NOTE: username x appid 数量太多，先不记录用户名
    extra2: '',
    extra3: data.appId
  });
  // 上报到 hive
  collectLog2Hive({
    duration: data.duration,
    beginTimestamp: data.beginTimestamp,
    endTimestamp: data.endTimestamp,
    modelType: data.modelType,
    sessionId: data.sessionId,
    stage: data.nodeType,
    requestId: data.requestId,
    username: data.username
  });
};
