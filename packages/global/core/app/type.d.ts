import type { FlowNodeTemplateType, StoreNodeItemType } from '../workflow/type';

import { AppTypeEnum } from './constants';
import { PermissionTypeEnum } from '../../support/permission/constant';
import { VariableInputEnum } from '../workflow/constants';
import { SelectedDatasetType } from '../workflow/api';
import { DatasetSearchModeEnum } from '../dataset/constants';
import { TeamTagSchema as TeamTagsSchemaType } from '../../support/user/team/type.d';
import { StoreEdgeItemType } from '../workflow/type/edge';

export interface AppSchema {
  _id: string;
  id: string;
  agentId: string;
  teamId: string;
  tmbId: string;
  name: string;
  type: `${AppTypeEnum}`;
  version?: 'v1' | 'v2';
  avatar: string;
  intro: string;
  updateTime: number;

  modules: StoreNodeItemType[];
  edges: StoreEdgeItemType[];

  // App system config
  scheduledTriggerConfig?: AppScheduledTriggerConfigType | null;
  scheduledTriggerNextTime?: Date;

  permission: `${PermissionTypeEnum}`;
  inited?: boolean;
  teamTags: string[];
}

export type AppListItemType = {
  _id: string;
  name: string;
  avatar: string;
  intro: string;
  isOwner: boolean;
  permission: `${PermissionTypeEnum}`;
};

export type AppDetailType = AppSchema & {
  agentId: string;
  id: string;
  isOwner: boolean;
  canWrite: boolean;
};

export type AppSimpleEditFormType = {
  // templateId: string;
  aiSettings: {
    model: string;
    systemPrompt?: string | undefined;
    temperature: number;
    maxToken: number;
    isResponseAnswerText: boolean;
    useInputFiles: boolean;
    maxHistories: number;
  };
  dataset: {
    datasets: SelectedDatasetType;
    searchMode: `${DatasetSearchModeEnum}`;
    similarity?: number;
    limit?: number;
    usingReRank?: boolean;
    datasetSearchUsingExtensionQuery?: boolean;
    datasetSearchExtensionModel?: string;
    datasetSearchExtensionBg?: string;
  };
  selectedTools: FlowNodeTemplateType[];
  userGuide: {
    welcomeText: string;
    variables: {
      id: string;
      key: string;
      label: string;
      type: `${VariableInputEnum}`;
      required: boolean;
      maxLen: number;
      enums: {
        value: string;
      }[];
    }[];
    questionGuide: boolean;
    recommendedQuestion: Array<string>;
    tts: {
      type: 'none' | 'web' | 'model';
      model?: string | undefined;
      voice?: string | undefined;
      speed?: number | undefined;
    };
    whisper: AppWhisperConfigType;
    scheduleTrigger: AppScheduledTriggerConfigType | null;
  };
};

/* app function config */
export type SettingAIDataType = {
  model: string;
  temperature: number;
  maxToken: number;
  isResponseAnswerText?: boolean;
  useInputFiles?: boolean;
  advancedQuote?: boolean;
  maxHistories?: number;
};

// variable
export type VariableItemType = {
  id: string;
  key: string;
  label: string;
  type: `${VariableInputEnum}`;
  required: boolean;
  maxLen: number;
  enums: { value: string }[];
};
// tts
export type AppTTSConfigType = {
  type: 'none' | 'web' | 'model';
  model?: string;
  voice?: string;
  speed?: number;
};
// whisper
export type AppWhisperConfigType = {
  open: boolean;
  autoSend: boolean;
  autoTTSResponse: boolean;
};
// interval timer
export type AppScheduledTriggerConfigType = {
  cronString: string;
  timezone: string;
  defaultPrompt: string;
};

interface Maintainer {
  avatarUrl: string;
  dept1: string;
  deptCode: string;
  name: string;
  username: string;
}

interface KnowledgeRepo {
  department: string;
  description: string;
  icon: string;
  id: number;
  knowledgeCount: number;
  maintainer: Maintainer[];
  name: string;
  publishDisableStatus: number;
  publishTime: number;
  refCount: number;
  status: string;
  updateTime: number;
  visibility: number;
}

interface RelationTool {
  code: string;
  description: string;
  icon: string;
  id: number;
  name: string;
  tuid: string;
  type: string;
}

interface Position {
  x: number;
  y: number;
}

interface InputOutput {
  debugLabel: string;
  description: string;
  id: string;
  key: string;
  label: string;
  max: number;
  min: number;
  placeholder: string;
  renderTypeList: string[];
  required: boolean;
  step: number;
  toolDescription: string;
  type: string;
  value: Record<string, unknown>;
  valueType: string;
}

interface WorkFlow {
  edges: StoreEdgeItemType[];
  id: number;
  nodes: StoreNodeItemType[];
  type: string;
  version: `v${number}`;
}

interface BaseAgent {
  id: number;
  auid: string;
  createTime: number;
  creator: string;
  dept: string;
  description: string;
  icon: string;
  greetingWords: string;
  maintainers: Maintainer[];
  name: string;
  parentAgentId: number;
  recommendedQuestions: string[];
  refCount: number;
  relationTools: RelationTool[];
  status: string;
  updateTime: number;
  updater: string;
}

export type SimpleAgent = BaseAgent & {
  agentMode: 'COMMON';
  autoCloseConversation: boolean;
  autoCloseTimeMs: number;
  contextRounds: number;
  historyPublished: boolean;
  knowledgeRepos: KnowledgeRepo[];
  modelType: string;
  roleDescription: string;
  temperature: number;
  topK: number;
};

export type WorkflowAgent = BaseAgent & {
  agentMode: 'WORKFLOW';
  workFlow: WorkFlow;
};

export type Agent = SimpleAgent | WorkflowAgent;

type AgentUpdateAll = Omit<Agent, 'maintainers'> & {
  maintainers: string[];
};

type RequiredSplit<T, K extends keyof T> = Required<Pick<T, K>> & Partial<Omit<T, K>>;

export type AgentUpdateParams = RequiredSplit<
  WorkflowAgent,
  'id' | 'auid' | 'maintainers' | 'name' | 'description' | 'icon'
>;

export interface LLMModelInfo {
  desc: string;
  disabled: boolean;
  disabledIcon: string;
  icon: string;
  maxLength: string;
  modelType: string;
  name: string;
  vip: boolean;
  maxInputTokens: number;
  maxOutputTokens: number;
  supportImg: boolean;
}

export interface UserInfo {
  avatarUrl: string;
  email: string;
  id: string;
  username: string;
  name: string;
}

export interface ModelConfig {
  maxContext: number;
  maxResponse: number;
}

export type FormatLLMModelInfo = Omit<LLMModelInfo, 'maxLength'> & ModelConfig;
