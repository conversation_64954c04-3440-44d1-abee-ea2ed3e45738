import type { ModelConfig } from '../../core/ai/model.d';

export const IS_PROD = process.env.APP_ENV === 'production';
export const IS_KWS = Boolean(process.env.KWS_SERVICE_NAME);
export const IS_KWS_CANDIDATE = IS_KWS && process.env.KWS_SERVICE_STAGE === 'CANDIDATE';
export const IS_KWS_STAGING = IS_KWS && process.env.KWS_SERVICE_STAGE === 'STAGING';
export const IS_KWS_PREONLINE = process.env.KWS_SERVICE_STAGE === 'PREONLINE';
export const IS_KWS_PROD = IS_KWS && process.env.KWS_SERVICE_STAGE === 'PROD';
export const KWS_LANE_ID = process.env.KWS_LANE_ID || '';
export const IS_LOCAL = !process.env.MY_POD_NAME;
export const IS_OVERSEA = !!process.env.IS_OVERSEA;
export const LLM_CALLING_KAFKA_TOPIC = 'ai_devops_gateway_calling_log';
export const CENSOR_DATA_KAFKA_TOPIC = 'kwaipilot_censor_data';

export enum ChatRoleEnum {
  System = 'System',
  Human = 'Human',
  AI = 'AI'
}
export const ChatRoleMap = {
  [ChatRoleEnum.System]: {
    name: '系统'
  },
  [ChatRoleEnum.Human]: {
    name: '用户'
  },
  [ChatRoleEnum.AI]: {
    name: 'AI'
  }
};

export enum ChatFileTypeEnum {
  image = 'image',
  file = 'file'
}
export enum ChatItemValueTypeEnum {
  text = 'text',
  file = 'file',
  tool = 'tool'
}

export enum ChatSourceEnum {
  test = 'test',
  online = 'online',
  share = 'share',
  api = 'api',
  team = 'team'
}
export const ChatSourceMap = {
  [ChatSourceEnum.test]: {
    name: 'core.chat.logs.test'
  },
  [ChatSourceEnum.online]: {
    name: 'core.chat.logs.online'
  },
  [ChatSourceEnum.share]: {
    name: 'core.chat.logs.share'
  },
  [ChatSourceEnum.api]: {
    name: 'core.chat.logs.api'
  },
  [ChatSourceEnum.team]: {
    name: 'core.chat.logs.team'
  }
};

export enum ChatStatusEnum {
  loading = 'loading',
  running = 'running',
  finish = 'finish'
}

export const IMG_BLOCK_KEY = 'img-block';
export const FILE_BLOCK_KEY = 'file-block';

export const MARKDOWN_QUOTE_SIGN = 'QUOTE SIGN';

export const DEFAULT_MODEL_CONFIG: ModelConfig = {
  maxContext: 4096,
  maxResponse: 4096,
  supportImg: false
};

export const FILE_CONTEXT_LENGTH_RATIO = 1.5;
