import { IS_LOCAL, IS_PROD } from '../../core/chat/constants';
import { FlowNodeTypeEnum } from './node/constant';

export enum FlowNodeTemplateTypeEnum {
  ai = 'ai',
  questionUnderstanding = 'questionUnderstanding',
  logic = 'logic',
  transform = 'transform',
  systemInput = 'systemInput',
  tools = 'tools',
  textAnswer = 'textAnswer',
  functionCall = 'functionCall',
  externalCall = 'externalCall',

  personalPlugin = 'personalPlugin',

  /** 研发能力相关 */
  team = 'team',
  gitlab = 'gitlab',
  kconf = 'kconf',
  other = 'other'
}

export enum WorkflowIOValueTypeEnum {
  string = 'string',
  number = 'number',
  boolean = 'boolean',
  object = 'object',
  arrayString = 'arrayString',
  arrayNumber = 'arrayNumber',
  arrayBoolean = 'arrayBoolean',
  arrayObject = 'arrayObject',
  any = 'any',
  none = 'none',

  chatHistory = 'chatHistory',
  datasetQuote = 'datasetQuote',

  dynamic = 'dynamic',

  // plugin special type
  selectApp = 'selectApp',
  selectDataset = 'selectDataset',
  selectDatasetV2 = 'selectDatasetV2',
  selectTool = 'selectTool',
  selectCodeSearch = 'selectCodeSearch'
}

/* reg: modulename key */
export enum NodeInputKeyEnum {
  // old
  welcomeText = 'welcomeText',
  switch = 'switch', // a trigger switch
  history = 'history',
  userChatInput = 'userChatInput',
  userChatInputFiles = 'userChatInputFiles',
  answerText = 'text',

  // system config
  questionGuide = 'questionGuide',
  // 推荐问题
  recommendedQuestion = 'recommendedQuestion',
  tts = 'tts',
  whisper = 'whisper',
  variables = 'variables',
  scheduleTrigger = 'scheduleTrigger',

  agents = 'agents', // cq agent key

  // latest
  // common
  aiModel = 'model',
  aiSystemPrompt = 'systemPrompt',
  description = 'description',
  anyInput = 'system_anyInput',
  textareaInput = 'system_textareaInput',
  addInputParam = 'system_addInputParam',

  // history
  historyMaxAmount = 'maxContext',

  // ai chat
  aiChatTemperature = 'temperature',
  aiChatMaxToken = 'maxToken',
  aiChatSettingModal = 'aiSettings',
  aiChatIsResponseText = 'isResponseAnswerText',
  aiChatUseInputFiles = 'useInputFiles',
  aiChatAdvancedQuote = 'advancedQuote',
  aiChatQuoteTemplate = 'quoteTemplate',
  aiChatQuotePrompt = 'quotePrompt',
  aiChatDatasetQuote = 'quoteQA',

  // dataset
  datasetSelectList = 'datasets',
  datasetSelectListV2 = 'datasetsV2',
  datasetSimilarity = 'similarity',
  datasetMaxTokens = 'limit',
  datasetSearchMode = 'searchMode',
  datasetSearchUsingReRank = 'usingReRank',
  datasetSearchUsingExtensionQuery = 'datasetSearchUsingExtensionQuery',
  datasetSearchExtensionModel = 'datasetSearchExtensionModel',
  datasetSearchExtensionBg = 'datasetSearchExtensionBg',

  // code search
  codeSearchSelectList = 'codeSearch',
  topK = 'topK',
  threshold = 'threshold',

  // tool
  toolSelect = 'toolSelect',

  // context extract
  contextExtractInput = 'content',
  extractKeys = 'extractKeys',

  // http
  httpReqUrl = 'system_httpReqUrl',
  httpTimeout = 'system_httpTimeout',
  httpHeaders = 'system_httpHeader',
  httpMethod = 'system_httpMethod',
  httpParams = 'system_httpParams',
  httpJsonBody = 'system_httpJsonBody',
  httpPath = 'system_httpPath',
  abandon_httpUrl = 'url',
  httpReqJsonSchema = 'httpReqJsonSchema',

  // kwaipilot tool
  versionId = 'versionId',
  identifier = 'identifier',

  // template-transform
  templateTransformInput = 'template_input',

  // app
  runAppSelectApp = 'app',

  // plugin
  pluginId = 'pluginId',
  pluginStart = 'pluginStart',

  // if else
  condition = 'condition',
  ifElseList = 'ifElseList',

  // kconf
  kconfKey = 'kconfKey',
  kconfEnv = 'kconfEnv',
  // group variable
  groupVariableInput = 'groupVariableInput',

  // multiAgent
  multiAgentSource = 'multiAgentSource',
  multiAgentIdentifier = 'multiAgentIdentifier',
  multiAgentIsResponseText = 'multiAgentIsResponseText',
  multiAgentUseInputFiles = 'multiAgentUseInputFiles',

  // streamRag
  streamRagSource = 'streamRagSource',
  streamRagIdentifier = 'streamRagIdentifier',
  streamRagIsResponseText = 'streamRagIsResponseText',
  streamRagMode = 'streamRagMode',
  streamRagParams = 'streamRagParams'
}

export enum NodeOutputKeyEnum {
  // common
  userChatInput = 'userChatInput',
  userChatInputFiles = 'userChatInputFiles',
  history = 'history',
  answerText = 'answerText', // module answer. the value will be show and save to history
  success = 'success',
  failed = 'failed',
  text = 'system_text',
  addOutputParam = 'system_addOutputParam',
  developResultKey = 'develop_resultKey',

  // dataset
  datasetQuoteQA = 'quoteQA',
  datasetQuoteQAV2 = 'quoteQAV2',

  // code search
  codeSearchQuotaQA = 'codeSearchQuotaQA',

  // web search
  webSearchQuotaQA = 'webSearchQuotaQA',

  // classify
  cqResult = 'cqResult',
  // context extract
  contextExtractFields = 'fields',

  // tf switch
  resultTrue = 'system_resultTrue',
  resultFalse = 'system_resultFalse',

  // tools
  selectedTools = 'selectedTools',

  // http
  httpRawResponse = 'httpRawResponse',
  httpRawResponseSchema = 'httpRawResponseSchema',

  // template transform
  templateTransformOutput = 'template_output',

  // plugin
  pluginStart = 'pluginStart',

  if = 'IF',
  else = 'ELSE',

  // kconf
  kconfResult = 'kconfResult',
  // group variable
  groupVariableOutput = 'groupVariableOutput',

  // streamRag
  streamRagIntent = 'streamRagIntent',
  streamRagMode = 'streamRagMode',
  streamRagResult = 'streamRagResult',
  streamRagQuoteChatInput = 'streamRagQuoteChatInput'
}

export enum VariableInputEnum {
  input = 'input',
  textarea = 'textarea',
  select = 'select',
  external = 'external'
}
export const variableMap = {
  [VariableInputEnum.input]: {
    icon: 'core/app/variable/input',
    title: 'core.module.variable.input type',
    desc: ''
  },
  [VariableInputEnum.textarea]: {
    icon: 'core/app/variable/textarea',
    title: 'core.module.variable.textarea type',
    desc: '允许用户最多输入4000字的对话框。'
  },
  [VariableInputEnum.select]: {
    icon: 'core/app/variable/select',
    title: 'core.module.variable.select type',
    desc: ''
  },
  [VariableInputEnum.external]: {
    icon: 'core/app/variable/external',
    title: 'core.module.variable.External type',
    desc: '可以通过API接口或分享链接的Query传递变量。增加该类型变量的主要目的是用于变量提示。使用例子: 你可以通过分享链接Query中拼接Token，来实现内部系统身份鉴权。'
  }
};

export const DYNAMIC_INPUT_REFERENCE_KEY = 'DYNAMIC_INPUT_REFERENCE_KEY';

/* run time */
export enum RuntimeEdgeStatusEnum {
  'waiting' = 'waiting',
  'active' = 'active',
  'skipped' = 'skipped'
}

export const VARIABLE_NODE_ID = 'VARIABLE_NODE_ID';

export const TemplateTypeTheme: Partial<Record<FlowNodeTemplateTypeEnum, string>> = {
  [FlowNodeTemplateTypeEnum.ai]: '#5ECCA7',
  [FlowNodeTemplateTypeEnum.questionUnderstanding]: '#00BDCE',
  [FlowNodeTemplateTypeEnum.logic]: '#FC801B',
  [FlowNodeTemplateTypeEnum.transform]: '#927EF2',
  [FlowNodeTemplateTypeEnum.tools]: '#FFAB02',
  [FlowNodeTemplateTypeEnum.team]: '#0362FF',
  [FlowNodeTemplateTypeEnum.gitlab]: '#FC6D26',
  [FlowNodeTemplateTypeEnum.kconf]: '#689EFB'
};

export const ModuleIcons = {
  Start:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/start.73ff1b8785bb8732.svg',
  GlobalVariable:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/variable.39b47a1410e92836.svg',
  IfElse:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/ifelse.6a665028fb470578.svg',
  AIChat:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/ai.952d8652ea03fe4a.svg',
  Reply:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/anwser.067073ed2dbf4740.svg',
  ClassifyQuestion:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/question.f4f93340074c90b6.svg',
  Extract:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/extract.c26bd0a8c8c1b896.svg',
  Dataset:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/knowledge.1d45ace5b9ca24b3.svg',
  HTTP: 'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/HTTP.e231c044ea05668a.svg',
  Kconf:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/kconf.cc1dbdf1eba8e9ab.svg',
  Tool: 'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/tool.6d973ea58276f95f.svg',
  SystemConfig:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/user-guide.31f7cf5b64fe0649.svg',
  CodeInterpreter:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/code.ac8266e33dbf190b.svg',
  Aggregation:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/agg.b5c4cfa13dbed7c8.svg',
  CodeSearch:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/Gitlab.5cb5666daffe1150.svg',
  Team: 'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/team.ccdaadc5c81a5836.svg',
  Gitlab:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/Gitlab.5cb5666daffe1150.svg',
  Template:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/Template.c2a185ad754becd1.svg'
} as const;

// 节点图标替换  兜底替换图标，兼容存量数据
export const ReplaceModuleIconMap: Partial<Record<FlowNodeTypeEnum, string>> = {
  [FlowNodeTypeEnum.globalVariable]: ModuleIcons.GlobalVariable,
  [FlowNodeTypeEnum.ifElseNode]: ModuleIcons.IfElse,
  [FlowNodeTypeEnum.answerNode]: ModuleIcons.Reply,
  [FlowNodeTypeEnum.classifyQuestion]: ModuleIcons.ClassifyQuestion,
  [FlowNodeTypeEnum.contentExtract]: ModuleIcons.Extract,
  [FlowNodeTypeEnum.httpRequest468]: ModuleIcons.HTTP,
  [FlowNodeTypeEnum.httpTool]: ModuleIcons.Tool,
  [FlowNodeTypeEnum.datasetSearchNodeV2]: ModuleIcons.Dataset,
  [FlowNodeTypeEnum.datasetSearchNode]: ModuleIcons.Dataset,
  [FlowNodeTypeEnum.kconf]: ModuleIcons.Kconf,
  [FlowNodeTypeEnum.codeInterpreter]: ModuleIcons.CodeInterpreter,
  [FlowNodeTypeEnum.templateTransformNode]: ModuleIcons.Template,
  [FlowNodeTypeEnum.systemConfig]: ModuleIcons.SystemConfig,
  [FlowNodeTypeEnum.chatNode]: ModuleIcons.AIChat,
  [FlowNodeTypeEnum.workflowStart]: ModuleIcons.Start,
  [FlowNodeTypeEnum.groupVariable]: ModuleIcons.Aggregation,
  [FlowNodeTypeEnum.codeSearch]: ModuleIcons.CodeSearch
};

let WEB_GEN_URL = 'http://kwaipilot-tool.internal/node/api/webpage/generate';
if (IS_LOCAL) {
  WEB_GEN_URL = 'http://localhost:8080/api/webpage/generate';
} else if (!IS_PROD) {
  WEB_GEN_URL = 'https://kwaipilot-tool.corp.kuaishou.com/node/api/webpage/generate';
}

const interpreterHost = IS_LOCAL
  ? 'http://localhost:3001'
  : !IS_PROD
    ? 'https://code-interpreter.staging.kuaishou.com'
    : 'http://code-interpreter.internal';
const CODE_INTERPRETER_URL = `${interpreterHost}/-/api/agent/chat/completions`;

const DATA_CODE_INTERPRETER_URL = `${interpreterHost}/-/dora/api/agent/chat/dataAnalyze`;
const IMAGE_CODE_INTERPRETER_URL = `${interpreterHost}/-/dora/api/agent/chat/imageAnalyze`;
const WEB_CODE_INTERPRETER_URL = `${interpreterHost}/-/dora/api/agent/web/generate`;

export const INTERNAL_AGENT_MAP: Record<string, string> = {
  WebGen: WEB_GEN_URL,
  CodeInterpreter: CODE_INTERPRETER_URL,
  DataCodeInterpreter: DATA_CODE_INTERPRETER_URL,
  ImageCodeInterpreter: IMAGE_CODE_INTERPRETER_URL,
  WebCodeInterpreter: WEB_CODE_INTERPRETER_URL
};
