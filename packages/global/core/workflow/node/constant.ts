export enum FlowNodeInputTypeEnum { // render ui
  reference = 'reference', // reference to other node output
  input = 'input', // one line input
  numberInput = 'numberInput',
  switch = 'switch', // true/false
  // 查询人员
  person = 'person',
  date = 'date',
  slider = 'slider',

  // editor
  textarea = 'textarea',
  JSONEditor = 'JSONEditor',

  JSEditor = 'JSEditor',

  addInputParam = 'addInputParam', // params input

  // special input
  selectApp = 'selectApp',

  // ai model select
  selectLLMModel = 'selectLLMModel',
  settingLLMModel = 'settingLLMModel',

  // dataset special input
  selectDataset = 'selectDataset',
  selectDatasetV2 = 'selectDatasetV2',
  selectDatasetParamsModal = 'selectDatasetParamsModal',
  settingDatasetQuotePrompt = 'settingDatasetQuotePrompt',

  // code search special input
  selectCodeSearch = 'selectCodeSearch',

  select = 'select',

  hidden = 'hidden',
  custom = 'custom'
}
export const FlowNodeInputMap: Record<
  FlowNodeInputTypeEnum,
  {
    icon: string;
  }
> = {
  [FlowNodeInputTypeEnum.reference]: {
    icon: 'core/workflow/inputType/reference'
  },
  [FlowNodeInputTypeEnum.input]: {
    icon: 'core/workflow/inputType/input'
  },
  [FlowNodeInputTypeEnum.numberInput]: {
    icon: 'core/workflow/inputType/numberInput'
  },
  [FlowNodeInputTypeEnum.select]: {
    icon: 'core/workflow/inputType/input'
  },
  [FlowNodeInputTypeEnum.switch]: {
    icon: 'core/workflow/inputType/switch'
  },
  [FlowNodeInputTypeEnum.textarea]: {
    icon: 'core/workflow/inputType/textarea'
  },
  [FlowNodeInputTypeEnum.JSONEditor]: {
    icon: 'core/workflow/inputType/jsonEditor'
  },
  [FlowNodeInputTypeEnum.addInputParam]: {
    icon: 'core/workflow/inputType/dynamic'
  },
  [FlowNodeInputTypeEnum.selectApp]: {
    icon: 'core/workflow/inputType/selectApp'
  },
  [FlowNodeInputTypeEnum.selectLLMModel]: {
    icon: 'core/workflow/inputType/selectLLM'
  },
  [FlowNodeInputTypeEnum.settingLLMModel]: {
    icon: 'core/workflow/inputType/selectLLM'
  },
  [FlowNodeInputTypeEnum.selectDataset]: {
    icon: 'core/workflow/inputType/selectDataset'
  },
  [FlowNodeInputTypeEnum.selectDatasetV2]: {
    icon: 'core/workflow/inputType/selectDataset'
  },
  [FlowNodeInputTypeEnum.selectDatasetParamsModal]: {
    icon: 'core/workflow/inputType/selectDataset'
  },
  [FlowNodeInputTypeEnum.settingDatasetQuotePrompt]: {
    icon: 'core/workflow/inputType/selectDataset'
  },
  [FlowNodeInputTypeEnum.hidden]: {
    icon: 'core/workflow/inputType/select'
  },
  [FlowNodeInputTypeEnum.custom]: {
    icon: 'core/workflow/inputType/select'
  },
  [FlowNodeInputTypeEnum.JSEditor]: {
    icon: 'core/workflow/inputType/jsonEditor'
  },
  [FlowNodeInputTypeEnum.selectCodeSearch]: {
    icon: 'core/workflow/inputType/selectDataset'
  },
  [FlowNodeInputTypeEnum.person]: {
    icon: 'core/workflow/inputType/input'
  },
  [FlowNodeInputTypeEnum.slider]: {
    icon: 'core/workflow/inputType/input'
  },
  [FlowNodeInputTypeEnum.date]: {
    icon: 'core/workflow/inputType/input'
  }
};

export enum FlowNodeOutputTypeEnum {
  hidden = 'hidden',
  source = 'source',
  static = 'static',
  dynamic = 'dynamic'
}

export enum FlowNodeTypeEnum {
  emptyNode = 'emptyNode',
  systemConfig = 'userGuide',
  globalVariable = 'globalVariable',
  workflowStart = 'workflowStart',
  chatNode = 'chatNode',
  /** 研发能力相关start */
  developTeamList = 'developTeamList',
  developTeamDetail = 'developTeamDetail',
  developTeamUpdateAssignee = 'developTeamUpdateAssignee',
  developTeamUpdateDate = 'developTeamUpdateDate',
  /** 研发能力相关end */

  datasetSearchNode = 'datasetSearchNode',
  datasetSearchNodeV2 = 'datasetSearchNodeV2',
  datasetConcatNode = 'datasetConcatNode',

  codeInterpreter = 'codeInterpreter',
  groupVariable = 'groupVariable',

  answerNode = 'answerNode',
  classifyQuestion = 'classifyQuestion',
  contentExtract = 'contentExtract',
  httpRequest468 = 'httpRequest468',
  httpTool = 'httpTool',
  kwaipilotTool = 'kwaipilotTool',
  runApp = 'app',
  pluginModule = 'pluginModule',
  pluginInput = 'pluginInput',
  pluginOutput = 'pluginOutput',
  queryExtension = 'cfr',
  stopTool = 'stopTool',
  templateTransformNode = 'templateTransformNode',
  lafModule = 'lafModule',
  ifElseNode = 'ifElseNode',
  kconf = 'kconf',
  toolsCall = 'toolsCall',
  multiAgent = 'multiAgent',
  codeSearch = 'codeSearch',
  streamRag = 'streamRag'
}

export const EDGE_TYPE = 'default';
