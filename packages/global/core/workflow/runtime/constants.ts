import { FlowNodeInputTypeEnum } from '../node/constant';

export enum SseResponseEventEnum {
  error = 'error',
  answer = 'answer', // animation stream
  fastAnswer = 'fastAnswer', // direct answer text, not animation
  flowNodeStatus = 'flowNodeStatus', // update node status
  warning = 'warning', // warning message 用于提示用户工作流执行过程中出现了一些预期之外的问题

  toolCall = 'toolCall', // tool start
  toolParams = 'toolParams', // tool params return
  toolResponse = 'toolResponse', // tool response return
  flowResponses = 'flowResponses', // sse response request

  datasetSearchResult = 'datasetSearchResult', // dataset search result
  datasetCodeSearchResult = 'datasetCodeSearchResult', // dataset code search result
  datasetWebSearchResult = 'datasetWebSearchResult', // dataset web search result

  quoteSource = 'quoteSource', // quote source

  artifact = 'artifact', // artifact
  cotStage = 'cotStage' // rag progress
}

export enum DispatchNodeResponseKeyEnum {
  skipHandleId = 'skipHandleId', // skip handle id
  nodeResponse = 'responseData', // run node response
  nodeDispatchUsages = 'nodeDispatchUsages', // the node bill.
  childrenResponses = 'childrenResponses', // Some nodes make recursive calls that need to be returned
  toolResponses = 'toolResponses', // The result is passed back to the tool node for use
  assistantResponses = 'assistantResponses', // assistant response
  artifacts = 'artifacts' // artifacts
}

export const needReplaceReferenceInputTypeList = [
  FlowNodeInputTypeEnum.reference,
  FlowNodeInputTypeEnum.settingDatasetQuotePrompt,
  FlowNodeInputTypeEnum.addInputParam,
  FlowNodeInputTypeEnum.custom
] as string[];
