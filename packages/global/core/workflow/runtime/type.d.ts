import { ChatNodeUsageType } from '../../../support/wallet/bill/type';
import { ChatItemValueItemType, ToolRunResponseItemType } from '../../chat/type';
import { FlowNodeInputItemType, FlowNodeOutputItemType } from '../type/io.d';
import { StoreNodeItemType } from '../type';
import { DispatchNodeResponseKeyEnum } from './constants';
import { StoreEdgeItemType } from '../type/edge';
import { NodeInputKeyEnum } from '../constants';
import { SearchDataResponseItemType } from '../../dataset/type';
import { ChatHistoryItemResType } from '../../../core/chat/type';
import { ClassifyQuestionAgentItemType } from '../../../core/workflow/type';
import { DatasetSearchModeEnum } from '../../dataset/constants';
import { ChatRoleEnum } from '../../../core/chat/constants';

export type RuntimeNodeItemType = {
  nodeId: StoreNodeItemType['nodeId'];
  name: StoreNodeItemType['name'];
  avatar: StoreNodeItemType['avatar'];
  intro?: StoreNodeItemType['intro'];
  flowNodeType: StoreNodeItemType['flowNodeType'];
  showStatus?: StoreNodeItemType['showStatus'];
  isEntry?: StoreNodeItemType['isEntry'];

  inputs: FlowNodeInputItemType[];
  outputs: FlowNodeOutputItemType[];

  pluginId?: string;
};

export type RuntimeEdgeItemType = StoreEdgeItemType & {
  status: 'waiting' | 'active' | 'skipped';
};

export type MultiAgentSource = 'kwaipilot' | 'internal' | 'external';

export type DispatchNodeResponseType = {
  // common
  moduleLogo?: string;
  runningTime?: number;
  query?: string;
  textOutput?: string;

  // bill
  tokens?: number;
  model?: string;
  contextTotalLen?: number;
  totalPoints?: number;

  // chat
  temperature?: number;
  maxToken?: number;
  quoteList?: SearchDataResponseItemType[];
  historyPreview?: {
    obj: `${ChatRoleEnum}`;
    value: string;
  }[]; // completion context array. history will slice

  // dataset search
  similarity?: number;
  limit?: number;
  searchMode?: `${DatasetSearchModeEnum}`;
  searchUsingReRank?: boolean;
  extensionModel?: string;
  extensionResult?: string;
  extensionTokens?: number;

  // cq
  cqList?: ClassifyQuestionAgentItemType[];
  cqResult?: string;

  // content extract
  extractDescription?: string;
  extractResult?: Record<string, any>;

  // http
  params?: Record<string, any>;
  body?: Record<string, any>;
  headers?: Record<string, any>;
  httpResult?: Record<string, any>;

  // plugin output
  pluginOutput?: Record<string, any>;
  pluginDetail?: ChatHistoryItemResType[];

  // if-else
  ifElseResult?: 'IF' | 'ELSE';

  // tool
  toolCallTokens?: number;
  toolDetail?: ChatHistoryItemResType[];
  toolStop?: boolean;

  // code interpreter
  codeInterpreterResult?: Record<string, any>;
  codeInterpreterInput?: Record<string, any>;

  // multi agent
  multiAgentSource?: MultiAgentSource;
  multiAgentIdentifier?: string;

  // streamRagQuoteChatInput
  streamRagQuoteChatInput?: string;
};

export type DispatchNodeResultType<T> = {
  [DispatchNodeResponseKeyEnum.skipHandleId]?: string[]; // skip some edge handle id
  [DispatchNodeResponseKeyEnum.nodeResponse]?: DispatchNodeResponseType; // The node response detail
  [DispatchNodeResponseKeyEnum.nodeDispatchUsages]?: ChatNodeUsageType[]; //
  [DispatchNodeResponseKeyEnum.childrenResponses]?: DispatchNodeResultType<T>[];
  [DispatchNodeResponseKeyEnum.toolResponses]?: ToolRunResponseItemType;
  [DispatchNodeResponseKeyEnum.assistantResponses]?: ChatItemValueItemType[];
} & T;

/* Single node props */
export type AIChatNodeProps = {
  [NodeInputKeyEnum.aiModel]: string;
  [NodeInputKeyEnum.aiSystemPrompt]?: string;
  [NodeInputKeyEnum.aiChatTemperature]: number;
  [NodeInputKeyEnum.aiChatMaxToken]: number;
  [NodeInputKeyEnum.aiChatIsResponseText]: boolean;
  [NodeInputKeyEnum.aiChatUseInputFiles]: boolean;
  [NodeInputKeyEnum.aiChatAdvancedQuote]: boolean;
  [NodeInputKeyEnum.aiChatQuoteTemplate]?: string;
  [NodeInputKeyEnum.aiChatQuotePrompt]?: string;
};

export type MultiAgentNodeProps = {
  [NodeInputKeyEnum.multiAgentSource]: MultiAgentSource;
  [NodeInputKeyEnum.multiAgentIdentifier]: string;
  [NodeInputKeyEnum.multiAgentIsResponseText]: boolean;
  [NodeInputKeyEnum.multiAgentUseInputFiles]: boolean;
};

export type ExtraConfig =
  | {
      contextPrompt?: string;
      webGen?: {
        tagName?: string;
        filePath?: string;
        selectApiIds?: string[];
      };
    }
  | undefined;
