import { ChatCompletionRequestMessageRoleEnum } from '../../ai/constants';
import { NodeInputKeyEnum, NodeOutputKeyEnum, WorkflowIOValueTypeEnum } from '../constants';
import { FlowNodeTypeEnum } from '../node/constant';
import { StoreNodeItemType } from '../type';
import { StoreEdgeItemType } from '../type/edge';
import { RuntimeEdgeItemType, RuntimeNodeItemType } from './type';
import { VARIABLE_NODE_ID } from '../constants';
import { ReferenceValueProps } from '../type/io';
import { ArtifactChatItem } from '../../chat/type';

export const initWorkflowEdgeStatus = (edges: StoreEdgeItemType[]): RuntimeEdgeItemType[] => {
  return (
    edges?.map((edge) => ({
      ...edge,
      status: 'waiting'
    })) || []
  );
};

export const getDefaultEntryNodeIds = (nodes: (StoreNodeItemType | RuntimeNodeItemType)[]) => {
  const entryList = [
    FlowNodeTypeEnum.systemConfig,
    FlowNodeTypeEnum.workflowStart,
    FlowNodeTypeEnum.pluginInput
  ];
  return nodes
    .filter((node) => entryList.includes(node.flowNodeType as any))
    .map((item) => item.nodeId);
};

export const storeNodes2RuntimeNodes = (
  nodes: StoreNodeItemType[],
  entryNodeIds: string[]
): RuntimeNodeItemType[] => {
  return (
    nodes.map<RuntimeNodeItemType>((node) => {
      return {
        nodeId: node.nodeId,
        name: node.name,
        avatar: node.avatar,
        intro: node.intro,
        flowNodeType: node.flowNodeType,
        showStatus: node.showStatus,
        isEntry: entryNodeIds.includes(node.nodeId),
        inputs: node.inputs,
        outputs: node.outputs,
        pluginId: node.pluginId
      };
    }) || []
  );
};

export const filterWorkflowEdges = (edges: RuntimeEdgeItemType[]) => {
  return edges.filter(
    (edge) =>
      edge.sourceHandle !== NodeOutputKeyEnum.selectedTools &&
      edge.targetHandle !== NodeOutputKeyEnum.selectedTools
  );
};

/* 
  区分普通连线和递归连线
  递归连线：可以通过往上查询 nodes，最终追溯到自身
*/
export const splitEdges2WorkflowEdges = ({
  edges,
  allEdges,
  currentNode
}: {
  edges: RuntimeEdgeItemType[];
  allEdges: RuntimeEdgeItemType[];
  currentNode: RuntimeNodeItemType;
}) => {
  const commonEdges: RuntimeEdgeItemType[] = [];
  const recursiveEdges: RuntimeEdgeItemType[] = [];

  edges.forEach((edge) => {
    const checkIsCurrentNode = (edge: RuntimeEdgeItemType): boolean => {
      const sourceEdge = allEdges.find((item) => item.target === edge.source);
      if (!sourceEdge) return false;
      if (sourceEdge.source === currentNode.nodeId) return true;
      return checkIsCurrentNode(sourceEdge);
    };
    if (checkIsCurrentNode(edge)) {
      recursiveEdges.push(edge);
    } else {
      commonEdges.push(edge);
    }
  });

  return { commonEdges, recursiveEdges };
};

/* 
  1. 输入线分类：普通线和递归线（可以追溯到自身）
  2. 起始线全部非 waiting 执行，或递归线全部非 waiting 执行
*/
export const checkNodeRunStatus = ({
  node,
  runtimeEdges
}: {
  node: RuntimeNodeItemType;
  runtimeEdges: RuntimeEdgeItemType[];
}) => {
  const workflowEdges = filterWorkflowEdges(runtimeEdges).filter(
    (item) => item.target === node.nodeId
  );

  if (workflowEdges.length === 0) {
    return 'run';
  }

  const { commonEdges, recursiveEdges } = splitEdges2WorkflowEdges({
    edges: workflowEdges,
    allEdges: runtimeEdges,
    currentNode: node
  });

  // check skip
  if (commonEdges.every((item) => item.status === 'skipped')) {
    return 'skip';
  }
  if (recursiveEdges.length > 0 && recursiveEdges.every((item) => item.status === 'skipped')) {
    return 'skip';
  }

  // check active
  if (commonEdges.every((item) => item.status !== 'waiting')) {
    return 'run';
  }
  if (recursiveEdges.length > 0 && recursiveEdges.every((item) => item.status !== 'waiting')) {
    return 'run';
  }

  return 'wait';
};

const getSingleRefrenceVariableValue = ({
  value,
  nodes,
  variables
}: {
  value: [string, string];
  nodes: RuntimeNodeItemType[];
  variables: Record<string, any>;
}) => {
  const sourceNodeId = value[0];
  const outputId = value[1];

  if (sourceNodeId === VARIABLE_NODE_ID && outputId) {
    return variables[outputId];
  }

  const node = nodes.find((node) => node.nodeId === sourceNodeId);

  if (!node) {
    return undefined;
  }

  const outputValue = node.outputs.find((output) => output.id === outputId)?.value;

  return outputValue;
};

export const getReferenceVariableValue = ({
  value,
  nodes,
  variables
}: {
  value?: ReferenceValueProps | Array<[string, string]>;
  nodes: RuntimeNodeItemType[];
  variables: Record<string, any>;
}) => {
  if (Array.isArray(value)) {
    if (value.length === 2 && typeof value[0] === 'string' && typeof value[1] === 'string') {
      return getSingleRefrenceVariableValue({ value: value as [string, string], nodes, variables });
    }

    // group variable
    if (
      (value as Array<any>).every(
        (it) => Array.isArray(it) && typeof it[0] === 'string' && typeof it[1] === 'string'
      )
    ) {
      return value.map((it) =>
        getSingleRefrenceVariableValue({ value: it as [string, string], nodes, variables })
      );
    }
  }

  return value;
};

export const textAdaptGptResponse = ({
  text,
  model = '',
  finish_reason = null,
  extraData = {}
}: {
  model?: string;
  text: string | null;
  finish_reason?: null | 'stop';
  extraData?: Object;
}) => {
  return JSON.stringify({
    ...extraData,
    id: '',
    object: '',
    created: 0,
    model,
    choices: [
      {
        delta:
          text === null
            ? {}
            : { role: ChatCompletionRequestMessageRoleEnum.Assistant, content: text },
        index: 0,
        finish_reason
      }
    ]
  });
};

export const formatVariableValByType = (val: any, valueType?: WorkflowIOValueTypeEnum) => {
  if (!valueType) return val;
  if (val === undefined || val === null) return;
  // Value type check, If valueType invalid, return undefined
  if (valueType.startsWith('array') && !Array.isArray(val)) return undefined;
  if (valueType === WorkflowIOValueTypeEnum.boolean) return Boolean(val);
  if (valueType === WorkflowIOValueTypeEnum.number) return Number(val);
  if (valueType === WorkflowIOValueTypeEnum.string) {
    return typeof val === 'object' ? JSON.stringify(val) : String(val);
  }
  if (
    [
      WorkflowIOValueTypeEnum.object,
      WorkflowIOValueTypeEnum.chatHistory,
      WorkflowIOValueTypeEnum.datasetQuote,
      WorkflowIOValueTypeEnum.selectApp,
      WorkflowIOValueTypeEnum.selectDataset
    ].includes(valueType) &&
    typeof val !== 'object'
  )
    return undefined;

  return val;
};

/* Replace the JSON string to reduce parsing errors
    1. Replace undefined values with null
    2. Replace newline strings
  */
export const replaceJsonBodyString = (
  text: string,
  variables: Record<string, any>,
  runtimeNodes: RuntimeNodeItemType[],
  allVariables: Record<string, any>
) => {
  // Check if the variable is in quotes
  const isVariableInQuotes = (text: string, variable: string) => {
    const index = text.indexOf(variable);
    if (index === -1) return false;

    // 计算变量前面的引号数量
    const textBeforeVar = text.substring(0, index);
    const matches = textBeforeVar.match(/"/g) || [];

    // 如果引号数量为奇数，则变量在引号内
    return matches.length % 2 === 1;
  };
  const valToStr = (val: any, isQuoted = false) => {
    if (val === undefined) return 'null';
    if (val === null) return 'null';

    if (typeof val === 'object') return JSON.stringify(val);

    if (typeof val === 'string') {
      if (isQuoted) {
        // Replace newlines with escaped newlines
        return val.replace(/\n/g, '\\n').replace(/(?<!\\)"/g, '\\"');
      }
      try {
        JSON.parse(val);
        return val;
      } catch (error) {
        const str = JSON.stringify(val);

        return str.startsWith('"') && str.endsWith('"') ? str.slice(1, -1) : str;
      }
    }

    return String(val);
  };

  // 1. Replace {{key.key}} variables
  const regex1 = /\{\{\$([^.]+)\.([^$]+)\$\}\}/g;
  const matches1 = [...text.matchAll(regex1)];
  matches1.forEach((match) => {
    const nodeId = match[1];
    const id = match[2];
    const fullMatch = match[0];

    // 检查变量是否在引号内
    const isInQuotes = isVariableInQuotes(text, fullMatch);

    const variableVal = (() => {
      if (nodeId === VARIABLE_NODE_ID) {
        return variables[id];
      }
      // Find upstream node input/output
      const node = runtimeNodes.find((node) => node.nodeId === nodeId);
      if (!node) return;

      const output = node.outputs.find((output) => output.id === id);
      if (output) return formatVariableValByType(output.value, output.valueType);

      const input = node.inputs.find((input) => input.key === id);
      if (input)
        return getReferenceVariableValue({ value: input.value, nodes: runtimeNodes, variables });
    })();

    const formatVal = valToStr(variableVal, isInQuotes);

    const regex = new RegExp(`\\{\\{\\$(${nodeId}\\.${id})\\$\\}\\}`, '');
    text = text.replace(regex, () => formatVal);
  });

  // 2. Replace {{key}} variables
  const regex2 = /{{([^}]+)}}/g;
  const matches2 = text.match(regex2) || [];
  const uniqueKeys2 = [...new Set(matches2.map((match) => match.slice(2, -2)))];
  for (const key of uniqueKeys2) {
    const fullMatch = `{{${key}}}`;
    // 检查变量是否在引号内
    const isInQuotes = isVariableInQuotes(text, fullMatch);

    text = text.replace(new RegExp(`{{(${key})}}`, ''), () =>
      valToStr(allVariables[key], isInQuotes)
    );
    // 特殊处理：如果变量在引号内且值为 null/undefined，需要移除引号
    if (allVariables[key] === null || allVariables[key] === undefined) {
      // 将 "null" 替换为 null（移除引号）
      text = text.replace(new RegExp(`"null"(?=\\s*[,}\\]])`, 'g'), 'null');
    }
  }

  return text.replace(/(".*?")\s*:\s*undefined\b/g, '$1:null');
};
