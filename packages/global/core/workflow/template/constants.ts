import { SystemConfigNode } from './system/systemConfig';
import { EmptyNode } from './system/emptyNode';
import { WorkflowStart } from './system/workflowStart';
import { AiChatModule } from './system/aiChat';
import { DatasetConcatModule } from './system/datasetConcat';
import { AssignedAnswerModule } from './system/assignedAnswer';
import { ClassifyQuestionModule } from './system/classifyQuestion';
import { ContextExtractModule } from './system/contextExtract';
import { HttpModule468 } from './system/http468';
import { HttpToolModule } from './system/httpTool';
import { KwaipilotToolModule } from './system/kwaipilotTool';
import { TemplateTransformModule } from './system/templateTransform';
import { StreamRagModule } from './system/streamRag';

import { ToolsCallModule } from './system/toolsCall';
import { StopToolNode } from './system/stopTool';

import { RunAppModule } from './system/runApp';
import { PluginInputModule } from './system/pluginInput';
import { PluginOutputModule } from './system/pluginOutput';
import { RunPluginModule } from './system/runPlugin';
import { AiQueryExtension } from './system/queryExtension';

import type { FlowNodeTemplateType } from '../type';
import { lafModule } from './system/laf';
import { ifElseNode } from './system/ifElse/index';
import { DatasetSearchModuleV2 } from './system/datasetSearchV2';
import { CodeInterpreterModule } from './system/codeInterpreter';

import { GroupVariableModule } from './system/groupVariable';
import { MultiAgentModule } from './system/multiAgent';
import { GetTaskListModule } from './develop/team/getTaskList';
import { GetTaskDetailModule } from './develop/team/getTaskDetail';
import { UpdateAssigneeModule } from './develop/team/updateAssignee';
import { CodeSearchModule } from './develop/codeSearch';
import { KconfModule } from './develop/kconf';
import { UpdateDateModule } from './develop/team/updateDate';
/* app flow module templates */
export const appSystemModuleTemplates: FlowNodeTemplateType[] = [
  SystemConfigNode,
  WorkflowStart,
  AiChatModule,
  AssignedAnswerModule,
  // DatasetSearchModule,
  DatasetSearchModuleV2,

  // DatasetConcatModule,
  // RunAppModule,
  // ToolsCallModule,
  StopToolNode,
  ClassifyQuestionModule,
  ContextExtractModule,
  CodeInterpreterModule,
  GroupVariableModule,
  HttpModule468,
  // HttpToolModule,
  KwaipilotToolModule,
  TemplateTransformModule,
  // AiQueryExtension,
  // lafModule,
  ifElseNode
  // NOTE: 暂时在前端屏蔽这两个节点
  // StreamRagModule,
  // MultiAgentModule,
  // CodeSearchModule
];

/* plugin flow module templates */
export const pluginSystemModuleTemplates: FlowNodeTemplateType[] = [
  PluginInputModule,
  PluginOutputModule,
  AiChatModule,
  AssignedAnswerModule,
  DatasetSearchModuleV2,
  // DatasetSearchModule,
  DatasetConcatModule,
  RunAppModule,
  ToolsCallModule,
  StopToolNode,
  ClassifyQuestionModule,
  ContextExtractModule,
  HttpModule468,
  HttpToolModule,
  KwaipilotToolModule,
  TemplateTransformModule,
  CodeSearchModule,
  StreamRagModule,
  AiQueryExtension,
  lafModule,
  ifElseNode
];

export const developModuleTemplates: FlowNodeTemplateType[] = [
  CodeSearchModule,
  KconfModule,
  GetTaskListModule,
  GetTaskDetailModule,
  UpdateAssigneeModule,
  UpdateDateModule
];

/* all module */
export const moduleTemplatesFlat: FlowNodeTemplateType[] = [
  ...developModuleTemplates,
  EmptyNode,
  SystemConfigNode,
  WorkflowStart,
  AiChatModule,
  // DatasetSearchModule,
  DatasetSearchModuleV2,
  DatasetConcatModule,
  AssignedAnswerModule,
  ClassifyQuestionModule,
  ContextExtractModule,
  HttpModule468,
  HttpToolModule,
  KwaipilotToolModule,
  TemplateTransformModule,
  CodeSearchModule,
  StreamRagModule,
  ToolsCallModule,
  StopToolNode,
  AiChatModule,
  RunAppModule,
  PluginInputModule,
  PluginOutputModule,
  RunPluginModule,
  AiQueryExtension,
  lafModule,
  ifElseNode,
  CodeInterpreterModule,
  KconfModule,
  GroupVariableModule,
  MultiAgentModule
];
