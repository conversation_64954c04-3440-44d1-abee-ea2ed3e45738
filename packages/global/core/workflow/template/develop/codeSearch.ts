import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '../../node/constant';
import { FlowNodeTemplateType } from '../../type';
import {
  WorkflowIOValueTypeEnum,
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  FlowNodeTemplateTypeEnum
} from '../../constants';
import { Input_Template_UserChatInput } from '../input';
import { getHandleConfig } from '../utils';
import { ModuleIcons } from '../../constants';

export const topKInput = {
  key: NodeInputKeyEnum.topK,
  renderTypeList: [FlowNodeInputTypeEnum.slider],
  valueType: WorkflowIOValueTypeEnum.number,
  label: '最大召回数量',
  description:
    '用于筛选与用户问题相似度最高的片段作为模型回答问题的参考，系统同时会根据选用模型上下文窗口大小动态调整分段数量。',
  value: 7,
  min: 1,
  max: 20,
  step: 1,
  markList: [
    { label: '1', value: 1 },
    { label: '20', value: 20 }
  ],
  required: true
};

export const thresholdInput = {
  key: NodeInputKeyEnum.threshold,
  renderTypeList: [FlowNodeInputTypeEnum.slider],
  valueType: WorkflowIOValueTypeEnum.number,
  label: '最低相关度',
  description: '用于设置片段筛选的相似度阈值，匹配度低于设定值的内容将不会被检索。',
  value: 0.01,
  min: 0.01,
  max: 0.99,
  step: 0.01,
  markList: [
    { label: '0.01', value: 0.01 },
    { label: '0.99', value: 0.99 }
  ],
  required: true
};
export const CodeSearchModule: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.codeSearch,
  templateType: FlowNodeTemplateTypeEnum.gitlab,
  flowNodeType: FlowNodeTypeEnum.codeSearch,
  sourceHandle: getHandleConfig(true, true, true, true),
  targetHandle: getHandleConfig(true, true, true, true),
  avatar: ModuleIcons.CodeSearch,
  name: '代码搜索',
  intro: '从选定代码库中，查询与用户问题相关的代码片段',
  showStatus: true,
  isTool: true,
  inputs: [
    {
      key: NodeInputKeyEnum.codeSearchSelectList,
      renderTypeList: [FlowNodeInputTypeEnum.selectCodeSearch, FlowNodeInputTypeEnum.reference],
      label: '选择代码库',
      value: [],
      valueType: WorkflowIOValueTypeEnum.arrayObject,
      list: [],
      description: '代码库参数格式为：{"repoName": string, "commitId": string}[]',
      required: true
    },
    {
      ...Input_Template_UserChatInput,
      toolDescription: '需要检索的内容'
    },
    topKInput,
    thresholdInput
  ],
  outputs: [
    {
      id: NodeOutputKeyEnum.codeSearchQuotaQA,
      key: NodeOutputKeyEnum.codeSearchQuotaQA,
      label: 'core.module.CodeSearch quote.label',
      type: FlowNodeOutputTypeEnum.static,
      valueType: WorkflowIOValueTypeEnum.arrayString
    }
  ]
};
