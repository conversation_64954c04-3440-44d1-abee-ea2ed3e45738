import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '../../../node/constant';
import { FlowNodeTemplateType } from '../../../type';
import {
  WorkflowIOValueTypeEnum,
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  FlowNodeTemplateTypeEnum
} from '../../../constants';
import { Input_Template_UserChatInput } from '../../input';
import { getHandleConfig } from '../../utils';
import { ModuleIcons } from '../../../constants';

export const GetTaskDetailModule: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.developTeamDetail,
  templateType: FlowNodeTemplateTypeEnum.team,
  flowNodeType: FlowNodeTypeEnum.developTeamDetail,
  sourceHandle: getHandleConfig(true, true, true, true),
  targetHandle: getHandleConfig(true, true, true, true),
  avatar: ModuleIcons.Team,
  name: '获取 Team 任务详情',
  intro: '根据任务 ID 获取指定的 Team 任务详细信息',
  showStatus: true,
  isTool: true,
  inputs: [
    {
      key: NodeInputKeyEnum.httpMethod,
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      valueType: WorkflowIOValueTypeEnum.string,
      label: '',
      value: 'GET',
      required: true
    },
    {
      key: NodeInputKeyEnum.httpPath,
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      valueType: WorkflowIOValueTypeEnum.string,
      label: '',
      description: 'core.module.input.description.Http Request Url',
      placeholder: 'https://api.ai.com/getInventory',
      value: '/pm/api/no-ba/external/task/taskInfo',
      required: true
    },
    {
      key: `${NodeInputKeyEnum.httpParams}.taskId`,
      valueType: WorkflowIOValueTypeEnum.string,
      label: '任务 Id',
      renderTypeList: [FlowNodeInputTypeEnum.input, FlowNodeInputTypeEnum.reference],
      description: '',
      canEdit: false,
      editField: { key: false, valueType: true },
      required: true
    },
    {
      key: `${NodeInputKeyEnum.httpParams}.operator`,
      valueType: WorkflowIOValueTypeEnum.string,
      label: '操作人',
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      description: '',
      canEdit: true,
      editField: { key: false, valueType: true },
      required: true,
      value: ['VARIABLE_NODE_ID', 'username']
    }
  ],
  outputs: [
    {
      id: 'result',
      key: 'result',
      label: '任务详情',
      type: FlowNodeOutputTypeEnum.static,
      // 详情里详细描述下面的字段
      description:
        '任务详情数据格式：{"taskId": string, "title": string, "description": string, "creator": string, "createTime": string, "assignee": string, "participants": string[], "startAt": string, "endAt": string, "completedAt": string, "updatedAt": string, "statusName": string, "statusPhase": string, "typeName": string, "typeClassName": string, "priorityName": string, "projectId": string, "projectName": string, "sectionName": string, "sectionId": string}',
      valueType: WorkflowIOValueTypeEnum.object
    },
    {
      id: NodeOutputKeyEnum.httpRawResponse,
      key: NodeOutputKeyEnum.httpRawResponse,
      label: '原始响应',
      description: '请求的原始响应。只能接受字符串或JSON类型响应数据',
      valueType: WorkflowIOValueTypeEnum.any,
      type: FlowNodeOutputTypeEnum.static
    }
  ]
};
