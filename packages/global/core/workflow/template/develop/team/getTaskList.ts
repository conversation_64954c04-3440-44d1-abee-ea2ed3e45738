import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '../../../node/constant';
import { FlowNodeTemplateType } from '../../../type';
import {
  WorkflowIOValueTypeEnum,
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  FlowNodeTemplateTypeEnum
} from '../../../constants';
import { Input_Template_UserChatInput } from '../../input';
import { getHandleConfig } from '../../utils';
import { ModuleIcons } from '../../../constants';

export const GetTaskListModule: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.developTeamList,
  templateType: FlowNodeTemplateTypeEnum.team,
  flowNodeType: FlowNodeTypeEnum.developTeamList,
  sourceHandle: getHandleConfig(true, true, true, true),
  targetHandle: getHandleConfig(true, true, true, true),
  avatar: ModuleIcons.Team,
  name: '获取 Team 任务列表',
  intro: '根据执行人或关键词获取 Team 任务列表，支持按任务类型过滤',
  showStatus: true,
  isTool: true,
  inputs: [
    {
      key: NodeInputKeyEnum.httpMethod,
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      valueType: WorkflowIOValueTypeEnum.string,
      label: '',
      value: 'POST',
      required: true
    },
    {
      key: NodeInputKeyEnum.httpPath,
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      valueType: WorkflowIOValueTypeEnum.string,
      label: '',
      description: 'core.module.input.description.Http Request Url',
      placeholder: 'https://api.ai.com/getInventory',
      value: '/pm/api/no-ba/external/task/query',
      required: true
    },
    {
      key: `${NodeInputKeyEnum.httpJsonBody}.keyword`,
      valueType: WorkflowIOValueTypeEnum.string,
      label: '关键词',
      renderTypeList: [FlowNodeInputTypeEnum.input, FlowNodeInputTypeEnum.reference],
      description: '',
      canEdit: false,
      editField: { key: false, valueType: true },
      required: false
    },
    {
      key: `${NodeInputKeyEnum.httpJsonBody}.limit`,
      valueType: WorkflowIOValueTypeEnum.number,
      label: '结果条数',
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      description: '',
      canEdit: false,
      editField: { key: false, valueType: true },
      required: true,
      value: 20
    },
    {
      key: `${NodeInputKeyEnum.httpJsonBody}.offset`,
      valueType: WorkflowIOValueTypeEnum.number,
      label: 'offset',
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      description: '',
      canEdit: false,
      editField: { key: false, valueType: true },
      required: true,
      value: 0
    },
    {
      key: `${NodeInputKeyEnum.httpJsonBody}.operator`,
      valueType: WorkflowIOValueTypeEnum.string,
      label: '操作人',
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      description: '',
      canEdit: true,
      editField: { key: false, valueType: true },
      required: true,
      value: ['VARIABLE_NODE_ID', 'username']
    }
  ],
  outputs: [
    {
      id: 'result',
      key: 'result',
      label: '任务列表',
      type: FlowNodeOutputTypeEnum.static,
      valueType: WorkflowIOValueTypeEnum.arrayObject
    },
    {
      id: NodeOutputKeyEnum.httpRawResponse,
      key: NodeOutputKeyEnum.httpRawResponse,
      label: '原始响应',
      description: '请求的原始响应。只能接受字符串或JSON类型响应数据',
      valueType: WorkflowIOValueTypeEnum.any,
      type: FlowNodeOutputTypeEnum.static
    }
  ]
};
