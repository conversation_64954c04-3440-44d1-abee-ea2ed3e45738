import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '../../../node/constant';
import { FlowNodeTemplateType } from '../../../type';
import {
  WorkflowIOValueTypeEnum,
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  FlowNodeTemplateTypeEnum
} from '../../../constants';
import { Input_Template_UserChatInput } from '../../input';
import { getHandleConfig } from '../../utils';
import { ModuleIcons } from '../../../constants';

export const UpdateAssigneeModule: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.developTeamUpdateAssignee,
  templateType: FlowNodeTemplateTypeEnum.team,
  flowNodeType: FlowNodeTypeEnum.developTeamUpdateAssignee,
  sourceHandle: getHandleConfig(true, true, true, true),
  targetHandle: getHandleConfig(true, true, true, true),
  avatar: ModuleIcons.Team,
  name: '更新 Team 任务执行人',
  intro: '更新指定 Team 任务的执行人',
  showStatus: true,
  isTool: true,
  inputs: [
    {
      key: NodeInputKeyEnum.httpMethod,
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      valueType: WorkflowIOValueTypeEnum.string,
      label: '',
      value: 'POST',
      required: true
    },
    {
      key: NodeInputKeyEnum.httpPath,
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      valueType: WorkflowIOValueTypeEnum.string,
      label: '',
      description: 'core.module.input.description.Http Request Url',
      // placeholder: 'https://api.ai.com/getInventory',
      value: '/pm/api/no-ba/external/task/modifyValues	',
      required: true
    },
    {
      key: `${NodeInputKeyEnum.httpJsonBody}.taskId`,
      valueType: WorkflowIOValueTypeEnum.string,
      label: '任务 Id',
      renderTypeList: [FlowNodeInputTypeEnum.input, FlowNodeInputTypeEnum.reference],
      description: '',
      canEdit: false,
      editField: { key: false, valueType: true },
      required: true
    },
    {
      key: `${NodeInputKeyEnum.httpJsonBody}.fields.assignee`,
      valueType: WorkflowIOValueTypeEnum.string,
      label: '执行人',
      renderTypeList: [FlowNodeInputTypeEnum.person, FlowNodeInputTypeEnum.reference],
      description: '',
      canEdit: false,
      editField: { key: false, valueType: true },
      required: true
    },
    {
      key: `${NodeInputKeyEnum.httpJsonBody}.operator`,
      valueType: WorkflowIOValueTypeEnum.string,
      label: '操作人',
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      description: '',
      canEdit: true,
      editField: { key: false, valueType: true },
      required: true,
      value: ['VARIABLE_NODE_ID', 'username']
    }
  ],
  outputs: [
    {
      id: 'code',
      key: 'code',
      label: '状态码',
      type: FlowNodeOutputTypeEnum.static,
      valueType: WorkflowIOValueTypeEnum.number
    },
    {
      id: NodeOutputKeyEnum.httpRawResponse,
      key: NodeOutputKeyEnum.httpRawResponse,
      label: '原始响应',
      description: '请求的原始响应。只能接受字符串或JSON类型响应数据',
      valueType: WorkflowIOValueTypeEnum.any,
      type: FlowNodeOutputTypeEnum.static
    }
  ]
};
