import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '../../../node/constant';
import { FlowNodeTemplateType } from '../../../type';
import {
  WorkflowIOValueTypeEnum,
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  FlowNodeTemplateTypeEnum
} from '../../../constants';
import { getHandleConfig } from '../../utils';
import { ModuleIcons } from '../../../constants';

export const UpdateDateModule: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.developTeamUpdateDate,
  templateType: FlowNodeTemplateTypeEnum.team,
  flowNodeType: FlowNodeTypeEnum.developTeamUpdateDate,
  sourceHandle: getHandleConfig(true, true, true, true),
  targetHandle: getHandleConfig(true, true, true, true),
  avatar: ModuleIcons.Team,
  name: '更新 Team 任务日期',
  intro: '更新指定 Team 任务的关键日期',
  showStatus: true,
  isTool: true,
  inputs: [
    {
      key: NodeInputKeyEnum.httpMethod,
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      valueType: WorkflowIOValueTypeEnum.string,
      label: '',
      value: 'POST',
      required: true
    },
    {
      key: NodeInputKeyEnum.httpPath,
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      valueType: WorkflowIOValueTypeEnum.string,
      label: '',
      description: 'core.module.input.description.Http Request Url',
      // placeholder: 'https://api.ai.com/getInventory',
      value: '/pm/api/no-ba/external/task/modifyValues	',
      required: true
    },
    {
      key: `${NodeInputKeyEnum.httpJsonBody}.taskId`,
      valueType: WorkflowIOValueTypeEnum.string,
      label: '任务 Id',
      renderTypeList: [FlowNodeInputTypeEnum.input, FlowNodeInputTypeEnum.reference],
      description: '',
      canEdit: false,
      editField: { key: false, valueType: true },
      required: true
    },
    {
      key: `${NodeInputKeyEnum.httpJsonBody}.fields.planDevBeginDate`,
      valueType: WorkflowIOValueTypeEnum.string,
      label: '计划开发开始日期',
      renderTypeList: [FlowNodeInputTypeEnum.date, FlowNodeInputTypeEnum.reference],
      description: '',
      canEdit: false,
      editField: { key: false, valueType: true },
      required: false
    },
    {
      key: `${NodeInputKeyEnum.httpJsonBody}.fields.planTogetherDebugBeginDate`,
      valueType: WorkflowIOValueTypeEnum.string,
      label: '计划联调开始日期',
      renderTypeList: [FlowNodeInputTypeEnum.date, FlowNodeInputTypeEnum.reference],
      description: '',
      canEdit: false,
      editField: { key: false, valueType: true },
      required: false
    },
    {
      key: `${NodeInputKeyEnum.httpJsonBody}.fields.planTestBeginDate`,
      valueType: WorkflowIOValueTypeEnum.string,
      label: '计划测试开始日期',
      renderTypeList: [FlowNodeInputTypeEnum.date, FlowNodeInputTypeEnum.reference],
      description: '',
      canEdit: false,
      editField: { key: false, valueType: true },
      required: false
    },
    {
      key: `${NodeInputKeyEnum.httpJsonBody}.fields.planTestAllDate`,
      valueType: WorkflowIOValueTypeEnum.string,
      label: '计划测试完成日期',
      renderTypeList: [FlowNodeInputTypeEnum.date, FlowNodeInputTypeEnum.reference],
      description: '',
      canEdit: false,
      editField: { key: false, valueType: true },
      required: false
    },
    {
      key: `${NodeInputKeyEnum.httpJsonBody}.fields.planTestDate`,
      valueType: WorkflowIOValueTypeEnum.string,
      label: '计划提测日期',
      renderTypeList: [FlowNodeInputTypeEnum.date, FlowNodeInputTypeEnum.reference],
      description: '',
      canEdit: false,
      editField: { key: false, valueType: true },
      required: false
    },
    {
      key: `${NodeInputKeyEnum.httpJsonBody}.fields.planOnlineDate`,
      valueType: WorkflowIOValueTypeEnum.string,
      label: '计划上线日期',
      renderTypeList: [FlowNodeInputTypeEnum.date, FlowNodeInputTypeEnum.reference],
      description: '',
      canEdit: false,
      editField: { key: false, valueType: true },
      required: false
    },
    {
      key: `${NodeInputKeyEnum.httpJsonBody}.fields.testBeginDate`,
      valueType: WorkflowIOValueTypeEnum.string,
      label: '测试开始日期',
      renderTypeList: [FlowNodeInputTypeEnum.date, FlowNodeInputTypeEnum.reference],
      description: '',
      canEdit: false,
      editField: { key: false, valueType: true },
      required: false
    },
    {
      key: `${NodeInputKeyEnum.httpJsonBody}.fields.testEndDate`,
      valueType: WorkflowIOValueTypeEnum.string,
      label: '测试结束日期',
      renderTypeList: [FlowNodeInputTypeEnum.date, FlowNodeInputTypeEnum.reference],
      description: '',
      canEdit: false,
      editField: { key: false, valueType: true },
      required: false
    },

    {
      key: `${NodeInputKeyEnum.httpJsonBody}.operator`,
      valueType: WorkflowIOValueTypeEnum.string,
      label: '操作人',
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      description: '',
      canEdit: true,
      editField: { key: false, valueType: true },
      required: true,
      value: ['VARIABLE_NODE_ID', 'username']
    }
  ],
  outputs: [
    {
      id: 'code',
      key: 'code',
      label: '状态码（0代表成功）',
      type: FlowNodeOutputTypeEnum.static,
      valueType: WorkflowIOValueTypeEnum.number
    },
    {
      id: NodeOutputKeyEnum.httpRawResponse,
      key: NodeOutputKeyEnum.httpRawResponse,
      label: '原始响应',
      description: '请求的原始响应。只能接受字符串或JSON类型响应数据',
      valueType: WorkflowIOValueTypeEnum.any,
      type: FlowNodeOutputTypeEnum.static
    }
  ]
};
