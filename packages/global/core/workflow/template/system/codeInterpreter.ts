import {
  FlowNodeTemplateTypeEnum,
  ModuleIcons,
  NodeInputKeyEnum,
  WorkflowIOValueTypeEnum
} from '../../constants';
import { FlowNodeInputTypeEnum, FlowNodeTypeEnum } from '../../node/constant';
import { FlowNodeTemplateType } from '../../type';
import { getHandleConfig } from '../utils';
import { Output_Template_AddOutput } from '../output';
import { Input_Template_DynamicInput } from '../input';

const defaultCode = `/**
 * input形如 { param1: any, param2: any }
 * 其中 param1 param2 定义在【自定义变量】处
 * 输出必须为对象形式，形如 { result1: any, result2: any }
 * 需要与【自定义输出】处定义保持一致
 */
function main (input) {
  // put your code here
  return {}
}
`;

export const CodeInterpreterModule: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.codeInterpreter,
  templateType: FlowNodeTemplateTypeEnum.transform,
  flowNodeType: FlowNodeTypeEnum.codeInterpreter,
  sourceHandle: getHandleConfig(true, true, true, true),
  targetHandle: getHandleConfig(true, true, true, true),
  avatar: ModuleIcons.CodeInterpreter,
  name: '代码执行器',
  intro: '根据输入执行 JavaScript 代码，实现自定义逻辑数据处理',
  inputs: [
    {
      ...Input_Template_DynamicInput,
      description: 'core.module.input.description.HTTP Dynamic Input',
      editField: {
        key: true,
        valueType: true
      }
    },
    {
      key: NodeInputKeyEnum.textareaInput,
      renderTypeList: [FlowNodeInputTypeEnum.JSEditor],
      valueType: WorkflowIOValueTypeEnum.string,
      label: '代码',
      description: '通过代码转换数据',
      value: defaultCode,
      required: true
    }
  ],
  outputs: [Output_Template_AddOutput]
};
