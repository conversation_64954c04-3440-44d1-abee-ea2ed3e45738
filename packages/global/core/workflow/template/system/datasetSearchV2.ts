import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '../../node/constant';
import { FlowNodeTemplateType } from '../../type';
import {
  WorkflowIOValueTypeEnum,
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  FlowNodeTemplateTypeEnum
} from '../../constants';
import { Input_Template_UserChatInput } from '../input';
import { DatasetSearchModeEnum } from '../../../dataset/constants';
import { getHandleConfig } from '../utils';
import { ModuleIcons } from '../../constants';

export const Dataset_SEARCH_DESC =
  '调用“语义检索”和“全文检索”能力，从“知识库”中查找可能与问题相关的参考内容';

export const topKInput = {
  key: NodeInputKeyEnum.topK,
  renderTypeList: [FlowNodeInputTypeEnum.slider],
  valueType: WorkflowIOValueTypeEnum.number,
  label: '最大召回数量',
  description:
    '用于筛选与用户问题相似度最高的片段作为模型回答问题的参考，系统同时会根据选用模型上下文窗口大小动态调整分段数量。',
  value: 7,
  min: 1,
  max: 20,
  step: 1,
  markList: [
    { label: '1', value: 1 },
    { label: '20', value: 20 }
  ],
  required: true
};

export const thresholdInput = {
  key: NodeInputKeyEnum.threshold,
  renderTypeList: [FlowNodeInputTypeEnum.slider],
  valueType: WorkflowIOValueTypeEnum.number,
  label: '最低相关度',
  description: '用于设置片段筛选的相似度阈值，匹配度低于设定值的内容将不会被检索。',
  value: 0.01,
  min: 0.01,
  max: 0.99,
  step: 0.01,
  markList: [
    { label: '0.01', value: 0.01 },
    { label: '0.99', value: 0.99 }
  ],
  required: true
};

export const DatasetSearchModuleV2: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.datasetSearchNodeV2,
  templateType: FlowNodeTemplateTypeEnum.ai,
  flowNodeType: FlowNodeTypeEnum.datasetSearchNodeV2,
  sourceHandle: getHandleConfig(true, true, true, true),
  targetHandle: getHandleConfig(true, true, true, true),
  avatar: ModuleIcons.Dataset,
  name: '知识库搜索',
  intro: '从选定知识库中，查询与用户问题相关的内容',
  showStatus: true,
  isTool: true,
  inputs: [
    {
      key: NodeInputKeyEnum.datasetSelectListV2,
      renderTypeList: [FlowNodeInputTypeEnum.selectDatasetV2],
      // renderTypeList: [FlowNodeInputTypeEnum.selectDataset, FlowNodeInputTypeEnum.reference],
      label: 'core.module.input.label.Select dataset',
      value: [],
      valueType: WorkflowIOValueTypeEnum.selectDatasetV2,
      list: [],
      required: true
    },
    topKInput,
    thresholdInput,
    // {
    //   key: NodeInputKeyEnum.datasetSimilarity,
    //   renderTypeList: [FlowNodeInputTypeEnum.selectDatasetParamsModal],
    //   label: '',
    //   value: 0.4,
    //   valueType: WorkflowIOValueTypeEnum.number
    // },
    // setting from modal
    // {
    //   key: NodeInputKeyEnum.datasetMaxTokens,
    //   renderTypeList: [FlowNodeInputTypeEnum.hidden],
    //   label: '',
    //   value: 1500,
    //   valueType: WorkflowIOValueTypeEnum.number
    // },
    // {
    //   key: NodeInputKeyEnum.datasetSearchMode,
    //   renderTypeList: [FlowNodeInputTypeEnum.hidden],
    //   label: '',
    //   valueType: WorkflowIOValueTypeEnum.string,
    //   value: DatasetSearchModeEnum.embedding
    // },
    // {
    //   key: NodeInputKeyEnum.datasetSearchUsingReRank,
    //   renderTypeList: [FlowNodeInputTypeEnum.hidden],
    //   label: '',
    //   valueType: WorkflowIOValueTypeEnum.boolean,
    //   value: false
    // },
    // {
    //   key: NodeInputKeyEnum.datasetSearchUsingExtensionQuery,
    //   renderTypeList: [FlowNodeInputTypeEnum.hidden],
    //   label: '',
    //   valueType: WorkflowIOValueTypeEnum.boolean,
    //   value: true
    // },
    // {
    //   key: NodeInputKeyEnum.datasetSearchExtensionModel,
    //   renderTypeList: [FlowNodeInputTypeEnum.hidden],
    //   label: '',
    //   valueType: WorkflowIOValueTypeEnum.string
    // },
    // {
    //   key: NodeInputKeyEnum.datasetSearchExtensionBg,
    //   renderTypeList: [FlowNodeInputTypeEnum.hidden],
    //   label: '',
    //   valueType: WorkflowIOValueTypeEnum.string,
    //   value: ''
    // },
    {
      ...Input_Template_UserChatInput,
      toolDescription: '需要检索的内容'
    }
  ],
  outputs: [
    // {
    //   id: NodeOutputKeyEnum.datasetQuoteQA,
    //   key: NodeOutputKeyEnum.datasetQuoteQA,
    //   label: 'core.module.Dataset quote.label',
    //   description: '特殊数组格式，搜索结果为空时，返回空数组。',
    //   type: FlowNodeOutputTypeEnum.static,
    //   valueType: WorkflowIOValueTypeEnum.datasetQuote
    // }
    {
      id: NodeOutputKeyEnum.datasetQuoteQAV2,
      key: NodeOutputKeyEnum.datasetQuoteQAV2,
      label: 'core.module.Dataset quote.label',
      // description: '特殊数组格式，搜索结果为空时，返回空数组。',
      type: FlowNodeOutputTypeEnum.static,
      valueType: WorkflowIOValueTypeEnum.arrayString
    }
  ]
};
