import {
  FlowNodeTemplateTypeEnum,
  ModuleIcons,
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  WorkflowIOValueTypeEnum
} from '../../constants';
import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '../../node/constant';
import { FlowNodeTemplateType } from '../../type';
import { getHandleConfig } from '../utils';
import { Output_Template_AddOutput } from '../output';

export const GroupVariableModule: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.groupVariable,
  templateType: FlowNodeTemplateTypeEnum.transform,
  flowNodeType: FlowNodeTypeEnum.groupVariable,
  sourceHandle: getHandleConfig(true, true, true, true),
  targetHandle: getHandleConfig(true, true, true, true),
  avatar: ModuleIcons.Aggregation,
  name: '变量聚合',
  intro: '将多个分支的变量聚合为一个变量，实现下游节点统一配置',
  inputs: [
    {
      key: NodeInputKeyEnum.groupVariableInput,
      renderTypeList: [FlowNodeInputTypeEnum.custom],
      valueType: WorkflowIOValueTypeEnum.any,
      label: '',
      required: true
    }
  ],
  outputs: [
    {
      id: NodeOutputKeyEnum.groupVariableOutput,
      key: NodeOutputKeyEnum.groupVariableOutput,
      label: '输出',
      type: FlowNodeOutputTypeEnum.static,
      valueType: WorkflowIOValueTypeEnum.any
    }
  ]
};
