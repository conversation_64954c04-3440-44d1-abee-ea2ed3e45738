import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '../../node/constant';
import { FlowNodeTemplateType } from '../../type';
import {
  WorkflowIOValueTypeEnum,
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  FlowNodeTemplateTypeEnum
} from '../../constants';
import { Input_Template_DynamicInput } from '../input';

import { getHandleConfig } from '../utils';
import { ModuleIcons } from '../../constants';

export const KwaipilotToolModule: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.kwaipilotTool,
  templateType: FlowNodeTemplateTypeEnum.ai,
  flowNodeType: FlowNodeTypeEnum.kwaipilotTool,
  sourceHandle: getHandleConfig(true, true, true, true),
  targetHandle: getHandleConfig(true, true, true, true),
  avatar: ModuleIcons.Tool,
  name: '工具',
  intro: '调用工具访问实时数据和执行操作',
  showStatus: true,
  isTool: true,
  inputs: [
    {
      key: NodeInputKeyEnum.toolSelect,
      renderTypeList: [FlowNodeInputTypeEnum.custom],
      label: '选择工具',
      valueType: WorkflowIOValueTypeEnum.selectTool,
      required: true
    },
    {
      ...Input_Template_DynamicInput,
      description: 'core.module.input.description.HTTP Dynamic Input',
      editField: {
        key: true,
        valueType: true
      }
    },
    {
      key: NodeInputKeyEnum.pluginId,
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      valueType: WorkflowIOValueTypeEnum.string,
      label: '',
      value: '',
      required: true
    },
    // {
    //   key: NodeInputKeyEnum.httpMethod,
    //   renderTypeList: [FlowNodeInputTypeEnum.custom],
    //   valueType: WorkflowIOValueTypeEnum.string,
    //   label: '',
    //   value: 'POST',
    //   required: true
    // },
    // {
    //   key: NodeInputKeyEnum.httpReqUrl,
    //   renderTypeList: [FlowNodeInputTypeEnum.hidden],
    //   valueType: WorkflowIOValueTypeEnum.string,
    //   label: '',
    //   description: 'core.module.input.description.Http Request Url',
    //   placeholder: 'https://api.ai.com/getInventory',
    //   required: false
    // },
    {
      key: NodeInputKeyEnum.httpHeaders,
      renderTypeList: [FlowNodeInputTypeEnum.custom],
      valueType: WorkflowIOValueTypeEnum.any,
      value: [],
      label: '',
      description: 'core.module.input.description.Http Request Header',
      placeholder: 'core.module.input.description.Http Request Header',
      required: false
    },
    {
      key: NodeInputKeyEnum.httpParams,
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      valueType: WorkflowIOValueTypeEnum.any,
      value: [],
      label: '',
      required: false
    },
    {
      key: NodeInputKeyEnum.httpPath,
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      valueType: WorkflowIOValueTypeEnum.any,
      value: [],
      label: '',
      required: false
    },
    {
      key: NodeInputKeyEnum.httpJsonBody,
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      valueType: WorkflowIOValueTypeEnum.any,
      value: '',
      label: '',
      required: false
    },
    {
      key: NodeInputKeyEnum.httpReqJsonSchema,
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      valueType: WorkflowIOValueTypeEnum.any,
      value: '',
      label: '',
      required: false
    },
    // 工具版本号
    {
      key: NodeInputKeyEnum.versionId,
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      valueType: WorkflowIOValueTypeEnum.number,
      label: '',
      value: -1,
      required: true
    },
    // 工具唯一标识
    {
      key: NodeInputKeyEnum.identifier,
      renderTypeList: [FlowNodeInputTypeEnum.hidden],
      valueType: WorkflowIOValueTypeEnum.string,
      label: '',
      value: '',
      required: true
    }
  ],
  outputs: [
    {
      id: NodeOutputKeyEnum.httpRawResponseSchema,
      key: NodeOutputKeyEnum.httpRawResponseSchema,
      type: FlowNodeOutputTypeEnum.hidden,
      valueType: WorkflowIOValueTypeEnum.any,
      label: ''
    },
    {
      id: NodeOutputKeyEnum.httpRawResponse,
      key: NodeOutputKeyEnum.httpRawResponse,
      label: '原始响应',
      description: 'HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。',
      valueType: WorkflowIOValueTypeEnum.any,
      type: FlowNodeOutputTypeEnum.static
    }
  ]
};
