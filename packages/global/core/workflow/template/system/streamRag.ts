import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '../../node/constant';
import { FlowNodeTemplateType } from '../../type';
import {
  WorkflowIOValueTypeEnum,
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  FlowNodeTemplateTypeEnum
} from '../../constants';
import { getHandleConfig } from '../utils';
import { ModuleIcons } from '../../constants';
import { Input_Template_UserChatInput } from '../input';

export const StreamRagModule: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.streamRag,
  templateType: FlowNodeTemplateTypeEnum.textAnswer,
  flowNodeType: FlowNodeTypeEnum.streamRag,
  sourceHandle: getHandleConfig(true, true, true, true),
  targetHandle: getHandleConfig(true, true, true, true),
  avatar: ModuleIcons.Dataset,
  name: '综合性 RAG',
  intro: '流式知识库搜索和问答',
  showStatus: true,
  isTool: true,
  inputs: [
    {
      key: NodeInputKeyEnum.streamRagMode,
      renderTypeList: [FlowNodeInputTypeEnum.select],
      label: '模式',
      value: '',
      valueType: WorkflowIOValueTypeEnum.string,
      list: [
        { value: 'common', label: 'common' },
        { value: 'web', label: 'web' },
        { value: 'code', label: 'code' },
        { value: 'docs', label: 'docs' }
      ],
      required: true
    },
    {
      key: NodeInputKeyEnum.streamRagParams,
      renderTypeList: [FlowNodeInputTypeEnum.input, FlowNodeInputTypeEnum.reference],
      label: 'RAG 参数',
      value: '',
      valueType: WorkflowIOValueTypeEnum.any,
      required: true
    },
    Input_Template_UserChatInput
  ],
  outputs: [
    {
      id: NodeOutputKeyEnum.streamRagIntent,
      key: NodeOutputKeyEnum.streamRagIntent,
      label: '意图',
      description: '对话意图',
      valueType: WorkflowIOValueTypeEnum.string,
      type: FlowNodeOutputTypeEnum.static
    },
    {
      id: NodeOutputKeyEnum.streamRagMode,
      key: NodeOutputKeyEnum.streamRagMode,
      label: '模式',
      description: '搜索模式',
      valueType: WorkflowIOValueTypeEnum.string,
      type: FlowNodeOutputTypeEnum.static
    },
    {
      id: NodeOutputKeyEnum.streamRagResult,
      key: NodeOutputKeyEnum.streamRagResult,
      label: '召回结果',
      description: '搜索召回结果',
      valueType: WorkflowIOValueTypeEnum.any,
      type: FlowNodeOutputTypeEnum.static
    },
    {
      id: NodeOutputKeyEnum.streamRagQuoteChatInput,
      key: NodeOutputKeyEnum.streamRagQuoteChatInput,
      label: '引用内容',
      description: '引用内容的问题',
      valueType: WorkflowIOValueTypeEnum.string,
      type: FlowNodeOutputTypeEnum.static
    }
  ]
};
