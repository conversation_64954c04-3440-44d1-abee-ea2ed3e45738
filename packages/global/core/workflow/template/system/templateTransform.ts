import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '../../node/constant';
import { FlowNodeTemplateType } from '../../type/index.d';
import {
  WorkflowIOValueTypeEnum,
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  FlowNodeTemplateTypeEnum
} from '../../constants';
import { Input_Template_DynamicInput } from '../input';

import { getHandleConfig } from '../utils';
import { ModuleIcons } from '../../constants';

const defaultTemplateStr = '{{ arg1 }}';

export const TemplateTransformModule: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.templateTransformNode,
  templateType: FlowNodeTemplateTypeEnum.transform,
  flowNodeType: FlowNodeTypeEnum.templateTransformNode,
  sourceHandle: getHandleConfig(true, true, true, true),
  targetHandle: getHandleConfig(true, true, true, true),
  avatar: ModuleIcons.Template,
  name: '模板转换',
  intro: '使用 EJS 模板语法，将数据转化为字符串，实现输出特定格式',
  showStatus: true,
  isTool: true,
  inputs: [
    {
      ...Input_Template_DynamicInput,
      description: '接受前面节点的输出值作为变量',
      editField: {
        key: true,
        valueType: true
      }
    },
    {
      key: NodeInputKeyEnum.textareaInput,
      renderTypeList: [FlowNodeInputTypeEnum.textarea],
      description: '只支持 EJS 模板引擎语法',
      valueType: WorkflowIOValueTypeEnum.string,
      value: defaultTemplateStr,
      label: '代码',
      required: true
    }
  ],
  outputs: [
    {
      id: NodeOutputKeyEnum.templateTransformOutput,
      key: NodeOutputKeyEnum.templateTransformOutput,
      label: '输出变量',
      description: '转换后内容',
      valueType: WorkflowIOValueTypeEnum.string,
      type: FlowNodeOutputTypeEnum.static
    }
  ]
};
