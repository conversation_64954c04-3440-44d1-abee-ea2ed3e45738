import {
  BedrockRuntimeClient,
  ContentBlock,
  ConversationRole,
  ConverseCommand,
  ConverseStreamCommand,
  ImageFormat,
  type SystemContentBlock,
  type Message,
  type Tool as ClaudeTool,
  type ConverseStreamCommandInput,
  type ConverseCommandInput
} from '@aws-sdk/client-bedrock-runtime';
import guess from 'magic-bytes.js';
import { getAWSConfig } from '../../common/kconf';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { ChatCompletionCreateParamsBase as OpenaiChatCompletionCreateParamsBase } from 'openai/resources/chat/completions';
import { NodeHttpHandler } from '@smithy/node-http-handler';
import { HttpsProxyAgent } from 'hpagent';
import { getOneProxy } from '../../common/proxy';
import { isIP } from 'net';
import { IS_LOCAL } from '@fastgpt/global/core/chat/constants';
import { installDNSCache } from '@fastgpt/global/common/dns';

const logger = createLogger('aws-claude');

async function getProxyAgent() {
  if (IS_LOCAL) {
    return new HttpsProxyAgent({ proxy: 'http://127.0.0.1:33210' });
  }

  const proxy = await getOneProxy('foreign-kix');
  if (proxy) {
    let host = proxy.host;
    if (!isIP(host) && host !== 'localhost') {
      logger.info(`dns lookup ${host}`);
      const cacheable = installDNSCache();
      const res = await cacheable.lookupAsync(host);
      logger.info(`dns lookup ${host} result ${res.address}`);
      host = res.address;
    }

    return new HttpsProxyAgent({
      proxy: `http://${host}:${proxy.port}`
    });
  }
}

async function getClientAndConfig(
  params: Omit<OpenaiChatCompletionCreateParamsBase, 'model'>
): Promise<{
  config: ConverseCommandInput | ConverseStreamCommandInput;
  client: BedrockRuntimeClient;
}> {
  const configList = await getAWSConfig();
  // 从 config 中随机取一个
  const config = configList[Math.floor(Math.random() * configList.length)];
  logger.info(`use api config ${JSON.stringify(config)}`);

  const agent = await getProxyAgent();
  const client = new BedrockRuntimeClient({
    region: config.region,
    credentials: {
      accessKeyId: config.accessKeyId,
      secretAccessKey: config.secretAccessKey
    },
    requestHandler: new NodeHttpHandler({
      httpAgent: agent,
      httpsAgent: agent
    })
  });
  const modelId = config.modelId;

  const systemContent: SystemContentBlock[] = [];

  logger.info('command params', { params });
  // 把 openai 的 message 转换为 aws claude 的 message
  const messages: Message[] = [];
  for (const m of params.messages) {
    // system message
    if (m.role === 'system') {
      systemContent.push({
        text: m.content
      });
    }
    // user message
    else if (m.role === 'user') {
      if (typeof m.content === 'string') {
        messages.push({
          role: ConversationRole.USER,
          content: [{ text: m.content ?? '' }]
        });
        continue;
      }

      const content: ContentBlock[] = [];
      m.content.forEach((c) => {
        // 文本内容
        if (c.type === 'text') {
          content.push({ text: c.text });
        }

        // 图片内容
        else if (c.type === 'image_url') {
          const [, base64] = c.image_url.url.split(';base64,');
          const source = Buffer.from(base64, 'base64');
          const u8a = new Uint8Array(source);
          const typesInfo = guess(u8a);

          let format: ImageFormat | null = null;
          if (typesInfo[0]?.typename === 'png') {
            format = ImageFormat.PNG;
          } else if (typesInfo[0]?.typename === 'gif') {
            format = ImageFormat.GIF;
          } else if (typesInfo[0]?.typename === 'webp') {
            format = ImageFormat.WEBP;
          } else if (typesInfo[0]?.typename === 'jpeg') {
            format = ImageFormat.JPEG;
          }
          if (format) {
            content.push({
              image: {
                format,
                source: {
                  bytes: u8a
                }
              }
            });
          } else {
            logger.error(`do not support image format ${typesInfo[0]?.typename}`);
          }
        }
      });

      messages.push({
        role: ConversationRole.USER,
        content
      });
    } else if (m.role === 'assistant') {
      messages.push({
        role: ConversationRole.ASSISTANT,
        content: [{ text: m.content ?? '' }]
      });
    }
    // tool message
    else if (m.role === 'tool') {
      messages.push({
        role: ConversationRole.USER,
        content: [
          {
            toolResult: {
              toolUseId: m.tool_call_id,
              content: [{ text: m.content }]
            }
          }
        ]
      });
    }
  }

  logger.info('converse messages', { messages });
  logger.info('converse systemContent', { systemContent });

  const claudeConfig: ConverseCommandInput | ConverseStreamCommandInput = {
    modelId,
    messages,
    inferenceConfig: {
      maxTokens: params.max_tokens ?? undefined,
      temperature: params.temperature ?? undefined,
      topP: params.top_p ?? undefined
    }
  };

  if (systemContent.length > 0) {
    claudeConfig.system = systemContent;
  }

  // 处理 tools 参数
  const claudeTools: ClaudeTool[] = [];
  const tools = params.tools ?? [];
  for (const t of tools) {
    if (t.type !== 'function') {
      continue;
    }

    claudeTools.push({
      toolSpec: {
        name: t.function.name,
        inputSchema: {
          json: t.function.parameters as any
        },
        description: t.function.description
      }
    });
  }
  if (claudeTools.length > 0) {
    claudeConfig.toolConfig = {
      tools: claudeTools
    };
    logger.info('converse tools', { tools });
  }

  if (typeof params.tool_choice === 'string' && claudeConfig.toolConfig) {
    if (params.tool_choice === 'auto') {
      claudeConfig.toolConfig.toolChoice = {
        auto: true
      };
    } else if ((params.tool_choice as any) === 'required') {
      claudeConfig.toolConfig.toolChoice = {
        any: true
      };
    }
  } else if (typeof params.tool_choice === 'object' && claudeConfig.toolConfig) {
    claudeConfig.toolConfig.toolChoice = {
      tool: {
        name: params.tool_choice.function.name
      }
    };
  }

  return {
    config: claudeConfig,
    client
  };
}

export type ConverseParams = Omit<OpenaiChatCompletionCreateParamsBase, 'model'>;

export async function converse(params: ConverseParams) {
  const { config, client } = await getClientAndConfig(params);
  const command = new ConverseCommand(config);
  return await client.send(command);
}

export async function converseStream(params: ConverseParams) {
  const { config, client } = await getClientAndConfig(params);
  const command = new ConverseStreamCommand(config);
  return await client.send(command);
}
