import { FilesCache } from '@fastgpt/global/common/cache';
import axios from 'axios';
import { guessBase64ImageType } from '../utils';
import { installDNSCache } from '@fastgpt/global/common/dns';
import { KError, ErrorTypeEnum } from '@fastgpt/global/common/error/kwaipilotError';

installDNSCache();
export async function getImageBase64Url(imageUrl: string): Promise<string> {
  const cached = FilesCache.get(imageUrl);
  if (cached) {
    return cached.rawText;
  }

  try {
    const response = await axios.get(imageUrl, {
      responseType: 'arraybuffer'
    });
    const base64 = Buffer.from(response.data).toString('base64');
    let imageType = response.headers['content-type'];
    if (imageType === undefined) {
      imageType = guessBase64ImageType(base64);
    }
    const base64url = `data:${imageType};base64,${base64}`;
    FilesCache.set(imageUrl, {
      rawText: base64url
    });
    return base64url;
  } catch (err) {
    throw new KError('BLOB_IMAGE_ERROR', 'engineError', {
      url: imageUrl,
      error: err
    });
  }
}
