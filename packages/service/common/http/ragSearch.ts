import { IS_KWS_PREONLINE, IS_PROD } from '@fastgpt/global/core/chat/constants';
import { post } from './base';
import { coreServicePerf, createLogger } from '@fastgpt/global/common/util/logger';

const logger = createLogger('rag-search');

const REQ_URL = IS_KWS_PREONLINE
  ? 'http://kwaipilot-tool.internal/node/api/search/rag'
  : IS_PROD
    ? 'http://kwaipilot-tool.internal/node/api/search/rag'
    : 'https://kwaipilot-tool.corp.kuaishou.com/node/api/search/rag';

export type RagSearchFile = {
  filename: string;
  fileIndex: number;
  pageNumber: number;
  content: string;
};

export type RagWebSearchItem = {
  title: string;
  link: string;
  date: string;
  favicon: string;
  content: string;
  prevContent: string;
  nextContent: string;
  fileIndex: number;
  pageNum: number;
};

export type RAGSearchToolResponse = {
  status: 'success' | 'error';
  message: string;
  results: RagWebSearchItem[];
};

export async function ragSearch(
  query: string,
  files: RagSearchFile[]
): Promise<RAGSearchToolResponse> {
  const startTime = +new Date();
  try {
    const res = await post<RAGSearchToolResponse>(REQ_URL, {
      body: {
        query,
        files
      }
    });
    coreServicePerf({
      subtag: 'rag-code-proxy',
      startTime,
      ok: true
    });
    return res;
  } catch (e: unknown) {
    logger.error(`rag search ${query} error: ${e}`, { error: e });
    coreServicePerf({
      subtag: 'rag-code-proxy',
      startTime,
      ok: false
    });
    throw e;
  }
}
