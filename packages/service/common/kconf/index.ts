import { IS_PROD } from '@fastgpt/global/core/chat/constants';
import { createKconf } from '@infra-node/kconf';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { LLM_CLUSTER_COMMON } from '@fastgpt/global/common/constants';
import type { ProxyRegion } from '../proxy';
import type { GatewayAppInfo } from '@fastgpt/global/core/workflow/type';

const logger = createLogger('common-kconf');
const kconfEnv = IS_PROD ? 'prod' : 'staging';

logger.info(`init kconf ${kconfEnv} ${process.env.APP_ENV}`);
export const kconf = createKconf({
  env: kconfEnv
});

type OpenaiAPIConfig = {
  baseUrl: string;
  apiKey: string;
  proxy: ProxyRegion;
  queries?: Record<string, string>;
  headers?: Record<string, string>;
};

type ClaudeAPIConfig = {
  region: string;
  accessKeyId: string;
  secretAccessKey: string;
  modelId: string;
};

export const getOpenAIConfig = async (provider: 'azure' | 'other') => {
  const config = await kconf.getJSONValue<{
    azureList: OpenaiAPIConfig[];
    otherList: OpenaiAPIConfig[];
  }>('kwaipilot.server.workflow_api_config');

  return provider === 'azure' ? config.azureList : config.otherList;
};

export const getAWSConfig = async () => {
  const config = await kconf.getJSONValue<{
    claudeList: ClaudeAPIConfig[];
  }>('kwaipilot.server.workflow_api_config');

  return config.claudeList;
};

export const getOverseaInternalApiWhitelist = async () => {
  const config = await kconf.getJSONValue<{
    rules: Array<{
      host: string;
      paths: string[];
    }>;
  }>('kwaipilot.platform.oversea_internal_api_whitelist');
  return config?.rules || [];
};

export type RagQuoteType = 'code' | 'file' | 'docs' | 'web';

export type QuoteType = RagQuoteType | 'simple';

type RagQuotePromptTemplates = {
  systemPrompt: string;
  userPrompt: string;
  noQuoteUserPrompt: string;
  toolsSystemPrompt?: string;
  deepSeekUserPrompt?: string;
  /** 上线修改kconf的deepSeekUserPrompt后可以将deepSeekUserPrompt删除掉 */
  deepSeekUserPromptV2?: string;
  systemPrompt40b?: string;
};

export const getRagSearchQuotePrompt = async (type: RagQuoteType) => {
  const config = await kconf.getJSONValue<Record<RagQuoteType, RagQuotePromptTemplates>>(
    'kwaipilot.server.rag_search_quote_prompt'
  );

  logger.info(`rag quotePrompt config: ${JSON.stringify(config)}`);
  const item = config[type];
  return {
    systemPrompt: item?.systemPrompt || '',
    userPrompt: item?.userPrompt || '',
    noQuoteUserPrompt: item?.noQuoteUserPrompt || '',
    deepSeekUserPrompt: item?.deepSeekUserPrompt || '',
    deepSeekUserPromptV2: item?.deepSeekUserPromptV2 || '',
    systemPrompt40b: item?.systemPrompt40b || '',
  };
};

export const getSimpleQuotePrompt = async () => {
  return await kconf.getStringValue('kwaipilot.server.quotePrompt');
};

// 获取当前网关应用，使用的集群
export async function getGatewayAppLLMCluster(appInfo: GatewayAppInfo): Promise<{
  cluster: string;
  fromGateway: boolean;
}> {
  if (!appInfo || !appInfo.id) {
    logger.info('appInfo is empty');
    return {
      cluster: LLM_CLUSTER_COMMON,
      fromGateway: false
    };
  }

  logger.info(`appId: ${appInfo.id}`);

  const appId = appInfo.id;
  const data = await kconf.getJSONValue<
    Record<
      string,
      {
        cluster?: string;
        provider?: string[];
      }
    >
  >('dmo.aidevops.unlimit-whitelist');

  logger.info(`data: ${JSON.stringify(data)}`);

  const config = data[appId];
  if (!config || !config.cluster || !config.provider) {
    logger.info('config, cluster or provider is missing, use common');
    return {
      cluster: LLM_CLUSTER_COMMON,
      fromGateway: true
    };
  }

  // 如果在网关调用时，没有配置 kwaipilot-agent 的 provider，说明走的通用限流，没有独立集群
  // 则使用 common 集群
  const provider = config.provider;
  if (!provider.includes('kwaipilot-agent')) {
    logger.info('provider is not kwaipilot-agent, use common');
    return {
      cluster: LLM_CLUSTER_COMMON,
      fromGateway: true
    };
  }

  const cluster = config.cluster;
  logger.info(`use cluster: ${cluster}`);

  return {
    cluster,
    fromGateway: true
  };
}

export async function getRagWebSearchToolSystemPrompt() {
  const config = await kconf.getJSONValue<Record<RagQuoteType, RagQuotePromptTemplates>>(
    'kwaipilot.server.rag_search_quote_prompt'
  );
  const item = config['web'];
  return item?.toolsSystemPrompt || '';
}

export async function useKwaipilot16k(agentId: string): Promise<boolean> {
  const config = await kconf.getJSONValue<{
    percent: number;
    whitelist: {
      agent: string[];
    };
  }>('dmo.aidevops.kwaipilot-16k-division');

  // 如果在白名单内，则不使用 16k
  if (config?.whitelist?.agent?.includes(agentId)) {
    return false;
  }

  const percent = config?.percent ?? 100;
  return Math.random() * 100 < percent;
}

export type ConsumerRegistry =
  | {
      type: 'kess';
      kessName: string;
    }
  | {
      type: 'standalone';
      endpoint: Array<{
        port: number;
        host: string;
      }>;
    };

export async function getKwaipilotModelClusterConfig() {
  const config = await kconf.getJSONValue<
    Record<
      string,
      Record<
        string,
        {
          registry: ConsumerRegistry;
        }
      >
    >
  >('dmo.aidevops.kwaipilot-model-cluster');
  return config;
}

// 获取kwaipilot 重试开关配置
export async function getKwaipilotRetryConfig() {
  const config = await kconf.getJSONValue<{ switch: 'on' | 'off' }>(
    'dmo.aidevops.kwaipilot-retry-config'
  );
  const isOpen = config?.switch === 'on';
  return isOpen;
}

// 获取kwaipilot 重试开关配置
export async function getOpenApiConfig(type: 'kwaipilot') {
  const config = await kconf.getJSONValue<{
    kwaipilot: {
      app_key: string;
      secret_key: string;
    };
  }>('dmo.aidevops.develop-openapi-config');
  return config?.[type];
}
