import { RPC, GRPCProtocol, KessRegistry, Consumer, StaticRegistry } from '@infra-node/rpc';
import { IConsumer } from './proto/functioncall_kwaipilot_grpc_pb';
import { TIMEOUT } from './const';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { type ConsumerRegistry } from '../../../common/kconf';

const logger = createLogger('llm-service-rpc');
const ConsumerStore = new Map<string, Consumer<IConsumer>>();

function getConsumerKey(registry: ConsumerRegistry) {
  if (registry.type === 'standalone') {
    const endpoints = registry.endpoint
      .map((i) => `${i.host}:${i.port}`)
      // 按字母排序
      .sort((a, b) => a.localeCompare(b));
    return `standalone://${endpoints.join(',')}`;
  }
  return `kess://${registry.kessName}`;
}

export async function getConsumer(registryConfig: ConsumerRegistry, timeout?: number) {
  const cacheKey = getConsumerKey(registryConfig) + (timeout || '');
  const cached = ConsumerStore.get(cacheKey);
  if (cached) {
    logger.info(`get consumer from store: ${cacheKey}`);
    return cached;
  }

  const registry =
    registryConfig.type === 'standalone'
      ? new StaticRegistry({
          endpoint: registryConfig.endpoint
        })
      : new KessRegistry({
          balance: 'round-robin',
          serviceName: registryConfig.kessName
        });
  const protocol = new GRPCProtocol({
    methodLowerCamelCase: true,
    protoLoader: {
      enums: Number,
      oneofs: false
    },
    proto: require('./proto/functioncall_kwaipilot_grpc_pb')
  });

  logger.info(`create consumer: ${cacheKey}`);

  const consumer = await RPC.createConsumer<IConsumer>({
    registry,
    protocol,
    timeout: timeout ?? TIMEOUT
  });

  logger.info(`set consumer to store: ${cacheKey}`);
  ConsumerStore.set(cacheKey, consumer);
  return consumer;
}

export * from './proto/functioncall_kwaipilot_pb';
