import { getLLMModelConsumer as getLLMModelConsumer16K } from './16k';
import { getConsumer } from './consumer';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { getKwaipilotModelClusterConfig, useKwaipilot16k } from '../../../common/kconf';
import { LLM_CLUSTER_COMMON } from '@fastgpt/global/common/constants';

const logger = createLogger('get-llm-model-consumer');

export async function getLLMModelConsumer(
  model: string,
  cluster: string,
  fromGateway: boolean,
  agentId: string,
  timeout?: number
) {
  logger.info(`try to get llm consumer ${model} ${cluster} ${fromGateway}`);

  // 如果集群是common，模型是kwaipilot_pro_32k，并且是从gateway来的，则可能使用16k集群
  if (cluster === LLM_CLUSTER_COMMON && model === 'kwaipilot_pro_32k' && fromGateway) {
    logger.info('maybe use 16k cluster');
    const use16k = await useKwaipilot16k(agentId);
    if (use16k) {
      logger.info('hit 16k cluster');
      return getLLMModelConsumer16K();
    }
  }

  const allConfig = await getKwaipilotModelClusterConfig();
  const clusterConfig = allConfig[cluster];
  if (!clusterConfig) {
    throw new Error(`no config for cluster: ${cluster}`);
  }

  const modelConfig = clusterConfig[model];
  if (!modelConfig) {
    throw new Error(`no config for model: ${model}, cluster: ${cluster}`);
  }

  const registryConfig = modelConfig.registry;
  if (!registryConfig) {
    throw new Error(`no registry config for model: ${model}, cluster: ${cluster}`);
  }

  const consumer = await getConsumer(registryConfig, timeout);
  if (!consumer) {
    throw new Error(`no consumer for model: ${model}, cluster: ${cluster}`);
  }

  logger.info(`get llm consumer success: ${model} ${cluster} ${fromGateway}`);
  return consumer;
}

export * from './proto/functioncall_kwaipilot_pb';
