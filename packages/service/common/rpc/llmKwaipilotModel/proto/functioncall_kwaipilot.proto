syntax = "proto3";

package kwaipilot_llm_api;
option java_multiple_files = true;

message KwaipilotLlmRequest {
    string request_body_json_str = 1;
}

message KwaipilotLlmResponse {
    string response_json_str = 1;
}

message KwaipilotTokenIds {
    repeated int32 token_ids = 1;
}

message <PERSON>wai<PERSON>lotEmbeddingRequest {
    repeated string texts = 1;
}

message Embedding {
    repeated float embedding = 1;
}

message EmbeddingResult {
    map <string, Embedding> embedding_result = 1;
}

message KwaipilotEmbeddingResponse {
    repeated EmbeddingResult embedding_results = 1;
}


service KwaipilotLlmApiService {
    rpc LlmChat(KwaipilotLlmRequest) returns (stream KwaipilotLlmResponse) {} // 模型chat接口
    rpc LlmGenerate(KwaipilotLlmRequest) returns (stream KwaipilotLlmResponse) {} // 模型补全接口
    rpc TokenizerEncode(KwaipilotLlmRequest) returns (KwaipilotTokenIds) {} // tokenize
    rpc TokenizerDecode(KwaipilotTokenIds) returns (KwaipilotLlmRequest) {} // detokenize
    rpc Embedding(KwaipilotEmbeddingRequest) returns (KwaipilotEmbeddingResponse) {} //embedding
}