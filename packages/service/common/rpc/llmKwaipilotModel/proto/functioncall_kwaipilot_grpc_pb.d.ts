// package: kwaipilot_llm_api
// file: functioncall_kwaipilot.proto

/* tslint:disable */
/* eslint-disable */

import * as grpc from '@grpc/grpc-js';
import * as functioncall_kwaipilot_pb from './functioncall_kwaipilot_pb';
import type { Metadata, RPCCallOptions } from '@infra-node/rpc';

export interface IKwaipilotLlmApiServiceClient {
  llmChat(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest,
    metadata?: Metadata
  ): grpc.ClientReadableStream<functioncall_kwaipilot_pb.KwaipilotLlmResponse>;
  llmChat(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest,
    metadata?: Metadata,
    options?: RPCCallOptions
  ): grpc.ClientReadableStream<functioncall_kwaipilot_pb.KwaipilotLlmResponse>;
  llmGenerate(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest,
    metadata?: Metadata
  ): grpc.ClientReadableStream<functioncall_kwaipilot_pb.KwaipilotLlmResponse>;
  llmGenerate(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest,
    metadata?: Metadata,
    options?: RPCCallOptions
  ): grpc.ClientReadableStream<functioncall_kwaipilot_pb.KwaipilotLlmResponse>;
  tokenizerEncode(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest,
    callback: (
      error: grpc.ServiceError | null,
      response: functioncall_kwaipilot_pb.KwaipilotTokenIds
    ) => void
  ): void;
  tokenizerEncode(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: functioncall_kwaipilot_pb.KwaipilotTokenIds
    ) => void
  ): void;
  tokenizerEncode(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: functioncall_kwaipilot_pb.KwaipilotTokenIds
    ) => void
  ): void;
  tokenizerDecode(
    request: functioncall_kwaipilot_pb.KwaipilotTokenIds,
    callback: (
      error: grpc.ServiceError | null,
      response: functioncall_kwaipilot_pb.KwaipilotLlmRequest
    ) => void
  ): void;
  tokenizerDecode(
    request: functioncall_kwaipilot_pb.KwaipilotTokenIds,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: functioncall_kwaipilot_pb.KwaipilotLlmRequest
    ) => void
  ): void;
  tokenizerDecode(
    request: functioncall_kwaipilot_pb.KwaipilotTokenIds,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: functioncall_kwaipilot_pb.KwaipilotLlmRequest
    ) => void
  ): void;
  embedding(
    request: functioncall_kwaipilot_pb.KwaipilotEmbeddingRequest,
    callback: (
      error: grpc.ServiceError | null,
      response: functioncall_kwaipilot_pb.KwaipilotEmbeddingResponse
    ) => void
  ): void;
  embedding(
    request: functioncall_kwaipilot_pb.KwaipilotEmbeddingRequest,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: functioncall_kwaipilot_pb.KwaipilotEmbeddingResponse
    ) => void
  ): void;
  embedding(
    request: functioncall_kwaipilot_pb.KwaipilotEmbeddingRequest,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: functioncall_kwaipilot_pb.KwaipilotEmbeddingResponse
    ) => void
  ): void;
}

export interface IKwaipilotLlmApiServiceClientPromisify {
  tokenizerEncode(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest
  ): Promise<functioncall_kwaipilot_pb.KwaipilotTokenIds>;
  tokenizerEncode(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest,
    metadata: Metadata
  ): Promise<functioncall_kwaipilot_pb.KwaipilotTokenIds>;
  tokenizerEncode(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<functioncall_kwaipilot_pb.KwaipilotTokenIds>;
  tokenizerDecode(
    request: functioncall_kwaipilot_pb.KwaipilotTokenIds
  ): Promise<functioncall_kwaipilot_pb.KwaipilotLlmRequest>;
  tokenizerDecode(
    request: functioncall_kwaipilot_pb.KwaipilotTokenIds,
    metadata: Metadata
  ): Promise<functioncall_kwaipilot_pb.KwaipilotLlmRequest>;
  tokenizerDecode(
    request: functioncall_kwaipilot_pb.KwaipilotTokenIds,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<functioncall_kwaipilot_pb.KwaipilotLlmRequest>;
  embedding(
    request: functioncall_kwaipilot_pb.KwaipilotEmbeddingRequest
  ): Promise<functioncall_kwaipilot_pb.KwaipilotEmbeddingResponse>;
  embedding(
    request: functioncall_kwaipilot_pb.KwaipilotEmbeddingRequest,
    metadata: Metadata
  ): Promise<functioncall_kwaipilot_pb.KwaipilotEmbeddingResponse>;
  embedding(
    request: functioncall_kwaipilot_pb.KwaipilotEmbeddingRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<functioncall_kwaipilot_pb.KwaipilotEmbeddingResponse>;
}

export interface IKwaipilotLlmApiServiceClientDynamic {
  llmChat(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest.DynamicObject,
    metadata?: Metadata
  ): grpc.ClientReadableStream<functioncall_kwaipilot_pb.KwaipilotLlmResponse.DynamicObject>;
  llmChat(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest.DynamicObject,
    metadata?: Metadata,
    options?: RPCCallOptions
  ): grpc.ClientReadableStream<functioncall_kwaipilot_pb.KwaipilotLlmResponse.DynamicObject>;
  llmGenerate(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest.DynamicObject,
    metadata?: Metadata
  ): grpc.ClientReadableStream<functioncall_kwaipilot_pb.KwaipilotLlmResponse.DynamicObject>;
  llmGenerate(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest.DynamicObject,
    metadata?: Metadata,
    options?: RPCCallOptions
  ): grpc.ClientReadableStream<functioncall_kwaipilot_pb.KwaipilotLlmResponse.DynamicObject>;
  tokenizerEncode(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: functioncall_kwaipilot_pb.KwaipilotTokenIds.DynamicObject
    ) => void
  ): void;
  tokenizerEncode(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: functioncall_kwaipilot_pb.KwaipilotTokenIds.DynamicObject
    ) => void
  ): void;
  tokenizerEncode(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: functioncall_kwaipilot_pb.KwaipilotTokenIds.DynamicObject
    ) => void
  ): void;
  tokenizerDecode(
    request: functioncall_kwaipilot_pb.KwaipilotTokenIds.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: functioncall_kwaipilot_pb.KwaipilotLlmRequest.DynamicObject
    ) => void
  ): void;
  tokenizerDecode(
    request: functioncall_kwaipilot_pb.KwaipilotTokenIds.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: functioncall_kwaipilot_pb.KwaipilotLlmRequest.DynamicObject
    ) => void
  ): void;
  tokenizerDecode(
    request: functioncall_kwaipilot_pb.KwaipilotTokenIds.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: functioncall_kwaipilot_pb.KwaipilotLlmRequest.DynamicObject
    ) => void
  ): void;
  embedding(
    request: functioncall_kwaipilot_pb.KwaipilotEmbeddingRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: functioncall_kwaipilot_pb.KwaipilotEmbeddingResponse.DynamicObject
    ) => void
  ): void;
  embedding(
    request: functioncall_kwaipilot_pb.KwaipilotEmbeddingRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: functioncall_kwaipilot_pb.KwaipilotEmbeddingResponse.DynamicObject
    ) => void
  ): void;
  embedding(
    request: functioncall_kwaipilot_pb.KwaipilotEmbeddingRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: functioncall_kwaipilot_pb.KwaipilotEmbeddingResponse.DynamicObject
    ) => void
  ): void;
}

export interface IKwaipilotLlmApiServiceClientDynamicPromisify {
  tokenizerEncode(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest.DynamicObject
  ): Promise<functioncall_kwaipilot_pb.KwaipilotTokenIds.DynamicObject>;
  tokenizerEncode(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest.DynamicObject,
    metadata: Metadata
  ): Promise<functioncall_kwaipilot_pb.KwaipilotTokenIds.DynamicObject>;
  tokenizerEncode(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<functioncall_kwaipilot_pb.KwaipilotTokenIds.DynamicObject>;
  tokenizerDecode(
    request: functioncall_kwaipilot_pb.KwaipilotTokenIds.DynamicObject
  ): Promise<functioncall_kwaipilot_pb.KwaipilotLlmRequest.DynamicObject>;
  tokenizerDecode(
    request: functioncall_kwaipilot_pb.KwaipilotTokenIds.DynamicObject,
    metadata: Metadata
  ): Promise<functioncall_kwaipilot_pb.KwaipilotLlmRequest.DynamicObject>;
  tokenizerDecode(
    request: functioncall_kwaipilot_pb.KwaipilotTokenIds.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<functioncall_kwaipilot_pb.KwaipilotLlmRequest.DynamicObject>;
  embedding(
    request: functioncall_kwaipilot_pb.KwaipilotEmbeddingRequest.DynamicObject
  ): Promise<functioncall_kwaipilot_pb.KwaipilotEmbeddingResponse.DynamicObject>;
  embedding(
    request: functioncall_kwaipilot_pb.KwaipilotEmbeddingRequest.DynamicObject,
    metadata: Metadata
  ): Promise<functioncall_kwaipilot_pb.KwaipilotEmbeddingResponse.DynamicObject>;
  embedding(
    request: functioncall_kwaipilot_pb.KwaipilotEmbeddingRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<functioncall_kwaipilot_pb.KwaipilotEmbeddingResponse.DynamicObject>;
}

export interface IConsumer {
  KwaipilotLlmApiService: IKwaipilotLlmApiServiceClient;
  __promise__: {
    KwaipilotLlmApiService: IKwaipilotLlmApiServiceClientPromisify;
  };
}

export interface IConsumerDynamic {
  KwaipilotLlmApiService: IKwaipilotLlmApiServiceClientDynamic;
  __promise__: {
    KwaipilotLlmApiService: IKwaipilotLlmApiServiceClientDynamicPromisify;
  };
}
