// GENERATED CODE -- DO NOT EDIT!

'use strict';
var grpc = require('@grpc/grpc-js');
var functioncall_kwaipilot_pb = require('./functioncall_kwaipilot_pb.js');

function serialize_kwaipilot_llm_api_KwaipilotEmbeddingRequest(arg) {
  if (!(arg instanceof functioncall_kwaipilot_pb.KwaipilotEmbeddingRequest)) {
    throw new Error('Expected argument of type kwaipilot_llm_api.KwaipilotEmbeddingRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_llm_api_KwaipilotEmbeddingRequest(buffer_arg) {
  return functioncall_kwaipilot_pb.KwaipilotEmbeddingRequest.deserializeBinary(
    new Uint8Array(buffer_arg)
  );
}

function serialize_kwaipilot_llm_api_KwaipilotEmbeddingResponse(arg) {
  if (!(arg instanceof functioncall_kwaipilot_pb.KwaipilotEmbeddingResponse)) {
    throw new Error('Expected argument of type kwaipilot_llm_api.KwaipilotEmbeddingResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_llm_api_KwaipilotEmbeddingResponse(buffer_arg) {
  return functioncall_kwaipilot_pb.KwaipilotEmbeddingResponse.deserializeBinary(
    new Uint8Array(buffer_arg)
  );
}

function serialize_kwaipilot_llm_api_KwaipilotLlmRequest(arg) {
  if (!(arg instanceof functioncall_kwaipilot_pb.KwaipilotLlmRequest)) {
    throw new Error('Expected argument of type kwaipilot_llm_api.KwaipilotLlmRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_llm_api_KwaipilotLlmRequest(buffer_arg) {
  return functioncall_kwaipilot_pb.KwaipilotLlmRequest.deserializeBinary(
    new Uint8Array(buffer_arg)
  );
}

function serialize_kwaipilot_llm_api_KwaipilotLlmResponse(arg) {
  if (!(arg instanceof functioncall_kwaipilot_pb.KwaipilotLlmResponse)) {
    throw new Error('Expected argument of type kwaipilot_llm_api.KwaipilotLlmResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_llm_api_KwaipilotLlmResponse(buffer_arg) {
  return functioncall_kwaipilot_pb.KwaipilotLlmResponse.deserializeBinary(
    new Uint8Array(buffer_arg)
  );
}

function serialize_kwaipilot_llm_api_KwaipilotTokenIds(arg) {
  if (!(arg instanceof functioncall_kwaipilot_pb.KwaipilotTokenIds)) {
    throw new Error('Expected argument of type kwaipilot_llm_api.KwaipilotTokenIds');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_llm_api_KwaipilotTokenIds(buffer_arg) {
  return functioncall_kwaipilot_pb.KwaipilotTokenIds.deserializeBinary(new Uint8Array(buffer_arg));
}

var KwaipilotLlmApiServiceService = (exports.KwaipilotLlmApiServiceService = {
  llmChat: {
    path: '/kwaipilot_llm_api.KwaipilotLlmApiService/LlmChat',
    requestStream: false,
    responseStream: true,
    requestType: functioncall_kwaipilot_pb.KwaipilotLlmRequest,
    responseType: functioncall_kwaipilot_pb.KwaipilotLlmResponse,
    requestSerialize: serialize_kwaipilot_llm_api_KwaipilotLlmRequest,
    requestDeserialize: deserialize_kwaipilot_llm_api_KwaipilotLlmRequest,
    responseSerialize: serialize_kwaipilot_llm_api_KwaipilotLlmResponse,
    responseDeserialize: deserialize_kwaipilot_llm_api_KwaipilotLlmResponse
  },
  llmGenerate: {
    path: '/kwaipilot_llm_api.KwaipilotLlmApiService/LlmGenerate',
    requestStream: false,
    responseStream: true,
    requestType: functioncall_kwaipilot_pb.KwaipilotLlmRequest,
    responseType: functioncall_kwaipilot_pb.KwaipilotLlmResponse,
    requestSerialize: serialize_kwaipilot_llm_api_KwaipilotLlmRequest,
    requestDeserialize: deserialize_kwaipilot_llm_api_KwaipilotLlmRequest,
    responseSerialize: serialize_kwaipilot_llm_api_KwaipilotLlmResponse,
    responseDeserialize: deserialize_kwaipilot_llm_api_KwaipilotLlmResponse
  },
  tokenizerEncode: {
    path: '/kwaipilot_llm_api.KwaipilotLlmApiService/TokenizerEncode',
    requestStream: false,
    responseStream: false,
    requestType: functioncall_kwaipilot_pb.KwaipilotLlmRequest,
    responseType: functioncall_kwaipilot_pb.KwaipilotTokenIds,
    requestSerialize: serialize_kwaipilot_llm_api_KwaipilotLlmRequest,
    requestDeserialize: deserialize_kwaipilot_llm_api_KwaipilotLlmRequest,
    responseSerialize: serialize_kwaipilot_llm_api_KwaipilotTokenIds,
    responseDeserialize: deserialize_kwaipilot_llm_api_KwaipilotTokenIds
  },
  tokenizerDecode: {
    path: '/kwaipilot_llm_api.KwaipilotLlmApiService/TokenizerDecode',
    requestStream: false,
    responseStream: false,
    requestType: functioncall_kwaipilot_pb.KwaipilotTokenIds,
    responseType: functioncall_kwaipilot_pb.KwaipilotLlmRequest,
    requestSerialize: serialize_kwaipilot_llm_api_KwaipilotTokenIds,
    requestDeserialize: deserialize_kwaipilot_llm_api_KwaipilotTokenIds,
    responseSerialize: serialize_kwaipilot_llm_api_KwaipilotLlmRequest,
    responseDeserialize: deserialize_kwaipilot_llm_api_KwaipilotLlmRequest
  },
  embedding: {
    path: '/kwaipilot_llm_api.KwaipilotLlmApiService/Embedding',
    requestStream: false,
    responseStream: false,
    requestType: functioncall_kwaipilot_pb.KwaipilotEmbeddingRequest,
    responseType: functioncall_kwaipilot_pb.KwaipilotEmbeddingResponse,
    requestSerialize: serialize_kwaipilot_llm_api_KwaipilotEmbeddingRequest,
    requestDeserialize: deserialize_kwaipilot_llm_api_KwaipilotEmbeddingRequest,
    responseSerialize: serialize_kwaipilot_llm_api_KwaipilotEmbeddingResponse,
    responseDeserialize: deserialize_kwaipilot_llm_api_KwaipilotEmbeddingResponse
  }
});

exports.KwaipilotLlmApiServiceClient = grpc.makeGenericClientConstructor(
  KwaipilotLlmApiServiceService
);
