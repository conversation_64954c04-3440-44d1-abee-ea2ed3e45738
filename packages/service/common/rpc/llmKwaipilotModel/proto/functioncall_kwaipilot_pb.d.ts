// package: kwaipilot_llm_api
// file: functioncall_kwaipilot.proto

/* tslint:disable */
/* eslint-disable */

import * as jspb from 'google-protobuf';

export class KwaipilotLlmRequest extends jspb.Message {
  getRequestBodyJsonStr(): string;
  setRequestBodyJsonStr(value: string): KwaipilotLlmRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KwaipilotLlmRequest.AsObject;
  static toObject(includeInstance: boolean, msg: KwaipilotLlmRequest): KwaipilotLlmRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: <PERSON>wai<PERSON>lotLlmRe<PERSON>, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): KwaipilotLlmRequest;
  static deserializeBinaryFromReader(
    message: Kwai<PERSON>lotLlmRequest,
    reader: jspb.BinaryReader
  ): KwaipilotLlmRequest;
}

export namespace KwaipilotLlmRequest {
  export type AsObject = {
    requestBodyJsonStr: string;
  };

  export type DynamicObject = {
    requestBodyJsonStr: string;
  };
}

export class KwaipilotLlmResponse extends jspb.Message {
  getResponseJsonStr(): string;
  setResponseJsonStr(value: string): KwaipilotLlmResponse;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KwaipilotLlmResponse.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KwaipilotLlmResponse
  ): KwaipilotLlmResponse.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: KwaipilotLlmResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): KwaipilotLlmResponse;
  static deserializeBinaryFromReader(
    message: KwaipilotLlmResponse,
    reader: jspb.BinaryReader
  ): KwaipilotLlmResponse;
}

export namespace KwaipilotLlmResponse {
  export type AsObject = {
    responseJsonStr: string;
  };

  export type DynamicObject = {
    responseJsonStr: string;
  };
}

export class KwaipilotTokenIds extends jspb.Message {
  clearTokenIdsList(): void;
  getTokenIdsList(): Array<number>;
  setTokenIdsList(value: Array<number>): KwaipilotTokenIds;
  addTokenIds(value: number, index?: number): number;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KwaipilotTokenIds.AsObject;
  static toObject(includeInstance: boolean, msg: KwaipilotTokenIds): KwaipilotTokenIds.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: KwaipilotTokenIds, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): KwaipilotTokenIds;
  static deserializeBinaryFromReader(
    message: KwaipilotTokenIds,
    reader: jspb.BinaryReader
  ): KwaipilotTokenIds;
}

export namespace KwaipilotTokenIds {
  export type AsObject = {
    tokenIdsList: Array<number>;
  };

  export type DynamicObject = {
    tokenIds: Array<number>;
  };
}

export class KwaipilotEmbeddingRequest extends jspb.Message {
  clearTextsList(): void;
  getTextsList(): Array<string>;
  setTextsList(value: Array<string>): KwaipilotEmbeddingRequest;
  addTexts(value: string, index?: number): string;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KwaipilotEmbeddingRequest.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KwaipilotEmbeddingRequest
  ): KwaipilotEmbeddingRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KwaipilotEmbeddingRequest,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KwaipilotEmbeddingRequest;
  static deserializeBinaryFromReader(
    message: KwaipilotEmbeddingRequest,
    reader: jspb.BinaryReader
  ): KwaipilotEmbeddingRequest;
}

export namespace KwaipilotEmbeddingRequest {
  export type AsObject = {
    textsList: Array<string>;
  };

  export type DynamicObject = {
    texts: Array<string>;
  };
}

export class Embedding extends jspb.Message {
  clearEmbeddingList(): void;
  getEmbeddingList(): Array<number>;
  setEmbeddingList(value: Array<number>): Embedding;
  addEmbedding(value: number, index?: number): number;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Embedding.AsObject;
  static toObject(includeInstance: boolean, msg: Embedding): Embedding.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: Embedding, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Embedding;
  static deserializeBinaryFromReader(message: Embedding, reader: jspb.BinaryReader): Embedding;
}

export namespace Embedding {
  export type AsObject = {
    embeddingList: Array<number>;
  };

  export type DynamicObject = {
    embedding: Array<number>;
  };
}

export class EmbeddingResult extends jspb.Message {
  getEmbeddingResultMap(): jspb.Map<string, Embedding>;
  clearEmbeddingResultMap(): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): EmbeddingResult.AsObject;
  static toObject(includeInstance: boolean, msg: EmbeddingResult): EmbeddingResult.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: EmbeddingResult, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): EmbeddingResult;
  static deserializeBinaryFromReader(
    message: EmbeddingResult,
    reader: jspb.BinaryReader
  ): EmbeddingResult;
}

export namespace EmbeddingResult {
  export type AsObject = {
    embeddingResultMap: Array<[string, Embedding.AsObject]>;
  };

  export type DynamicObject = {
    embeddingResult: Record<string, Embedding.DynamicObject>;
  };
}

export class KwaipilotEmbeddingResponse extends jspb.Message {
  clearEmbeddingResultsList(): void;
  getEmbeddingResultsList(): Array<EmbeddingResult>;
  setEmbeddingResultsList(value: Array<EmbeddingResult>): KwaipilotEmbeddingResponse;
  addEmbeddingResults(value?: EmbeddingResult, index?: number): EmbeddingResult;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KwaipilotEmbeddingResponse.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KwaipilotEmbeddingResponse
  ): KwaipilotEmbeddingResponse.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KwaipilotEmbeddingResponse,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KwaipilotEmbeddingResponse;
  static deserializeBinaryFromReader(
    message: KwaipilotEmbeddingResponse,
    reader: jspb.BinaryReader
  ): KwaipilotEmbeddingResponse;
}

export namespace KwaipilotEmbeddingResponse {
  export type AsObject = {
    embeddingResultsList: Array<EmbeddingResult.AsObject>;
  };

  export type DynamicObject = {
    embeddingResults: Array<EmbeddingResult.DynamicObject>;
  };
}
