// source: functioncall_kwaipilot.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.kwaipilot_llm_api.Embedding', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.EmbeddingResult', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.KwaipilotLlmRequest', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.KwaipilotLlmResponse', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.KwaipilotTokenIds', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest = function (opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot_llm_api.KwaipilotLlmRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.KwaipilotLlmRequest.displayName =
    'proto.kwaipilot_llm_api.KwaipilotLlmRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse = function (opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot_llm_api.KwaipilotLlmResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.KwaipilotLlmResponse.displayName =
    'proto.kwaipilot_llm_api.KwaipilotLlmResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds = function (opt_data) {
  jspb.Message.initialize(
    this,
    opt_data,
    0,
    -1,
    proto.kwaipilot_llm_api.KwaipilotTokenIds.repeatedFields_,
    null
  );
};
goog.inherits(proto.kwaipilot_llm_api.KwaipilotTokenIds, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.KwaipilotTokenIds.displayName =
    'proto.kwaipilot_llm_api.KwaipilotTokenIds';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest = function (opt_data) {
  jspb.Message.initialize(
    this,
    opt_data,
    0,
    -1,
    proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.repeatedFields_,
    null
  );
};
goog.inherits(proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.displayName =
    'proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.Embedding = function (opt_data) {
  jspb.Message.initialize(
    this,
    opt_data,
    0,
    -1,
    proto.kwaipilot_llm_api.Embedding.repeatedFields_,
    null
  );
};
goog.inherits(proto.kwaipilot_llm_api.Embedding, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.Embedding.displayName = 'proto.kwaipilot_llm_api.Embedding';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.EmbeddingResult = function (opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot_llm_api.EmbeddingResult, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.EmbeddingResult.displayName = 'proto.kwaipilot_llm_api.EmbeddingResult';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse = function (opt_data) {
  jspb.Message.initialize(
    this,
    opt_data,
    0,
    -1,
    proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.repeatedFields_,
    null
  );
};
goog.inherits(proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.displayName =
    'proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse';
}

if (jspb.Message.GENERATE_TO_OBJECT) {
  /**
   * Creates an object representation of this proto.
   * Field names that are reserved in JavaScript and will be renamed to pb_name.
   * Optional fields that are not set will be set to undefined.
   * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
   * For the list of reserved names please see:
   *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
   * @param {boolean=} opt_includeInstance Deprecated. whether to include the
   *     JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @return {!Object}
   */
  proto.kwaipilot_llm_api.KwaipilotLlmRequest.prototype.toObject = function (opt_includeInstance) {
    return proto.kwaipilot_llm_api.KwaipilotLlmRequest.toObject(opt_includeInstance, this);
  };

  /**
   * Static version of the {@see toObject} method.
   * @param {boolean|undefined} includeInstance Deprecated. Whether to include
   *     the JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @param {!proto.kwaipilot_llm_api.KwaipilotLlmRequest} msg The msg instance to transform.
   * @return {!Object}
   * @suppress {unusedLocalVariables} f is only used for nested messages
   */
  proto.kwaipilot_llm_api.KwaipilotLlmRequest.toObject = function (includeInstance, msg) {
    var f,
      obj = {
        requestBodyJsonStr: jspb.Message.getFieldWithDefault(msg, 1, '')
      };

    if (includeInstance) {
      obj.$jspbMessageInstance = msg;
    }
    return obj;
  };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.KwaipilotLlmRequest}
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.deserializeBinary = function (bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.KwaipilotLlmRequest();
  return proto.kwaipilot_llm_api.KwaipilotLlmRequest.deserializeBinaryFromReader(msg, reader);
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.KwaipilotLlmRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.KwaipilotLlmRequest}
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.deserializeBinaryFromReader = function (msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
      case 1:
        var value = /** @type {string} */ (reader.readString());
        msg.setRequestBodyJsonStr(value);
        break;
      default:
        reader.skipField();
        break;
    }
  }
  return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.prototype.serializeBinary = function () {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.KwaipilotLlmRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.KwaipilotLlmRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.serializeBinaryToWriter = function (message, writer) {
  var f = undefined;
  f = message.getRequestBodyJsonStr();
  if (f.length > 0) {
    writer.writeString(1, f);
  }
};

/**
 * optional string request_body_json_str = 1;
 * @return {string}
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.prototype.getRequestBodyJsonStr = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotLlmRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.prototype.setRequestBodyJsonStr = function (value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};

if (jspb.Message.GENERATE_TO_OBJECT) {
  /**
   * Creates an object representation of this proto.
   * Field names that are reserved in JavaScript and will be renamed to pb_name.
   * Optional fields that are not set will be set to undefined.
   * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
   * For the list of reserved names please see:
   *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
   * @param {boolean=} opt_includeInstance Deprecated. whether to include the
   *     JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @return {!Object}
   */
  proto.kwaipilot_llm_api.KwaipilotLlmResponse.prototype.toObject = function (opt_includeInstance) {
    return proto.kwaipilot_llm_api.KwaipilotLlmResponse.toObject(opt_includeInstance, this);
  };

  /**
   * Static version of the {@see toObject} method.
   * @param {boolean|undefined} includeInstance Deprecated. Whether to include
   *     the JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @param {!proto.kwaipilot_llm_api.KwaipilotLlmResponse} msg The msg instance to transform.
   * @return {!Object}
   * @suppress {unusedLocalVariables} f is only used for nested messages
   */
  proto.kwaipilot_llm_api.KwaipilotLlmResponse.toObject = function (includeInstance, msg) {
    var f,
      obj = {
        responseJsonStr: jspb.Message.getFieldWithDefault(msg, 1, '')
      };

    if (includeInstance) {
      obj.$jspbMessageInstance = msg;
    }
    return obj;
  };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.KwaipilotLlmResponse}
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.deserializeBinary = function (bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.KwaipilotLlmResponse();
  return proto.kwaipilot_llm_api.KwaipilotLlmResponse.deserializeBinaryFromReader(msg, reader);
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.KwaipilotLlmResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.KwaipilotLlmResponse}
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.deserializeBinaryFromReader = function (msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
      case 1:
        var value = /** @type {string} */ (reader.readString());
        msg.setResponseJsonStr(value);
        break;
      default:
        reader.skipField();
        break;
    }
  }
  return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.prototype.serializeBinary = function () {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.KwaipilotLlmResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.KwaipilotLlmResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.serializeBinaryToWriter = function (message, writer) {
  var f = undefined;
  f = message.getResponseJsonStr();
  if (f.length > 0) {
    writer.writeString(1, f);
  }
};

/**
 * optional string response_json_str = 1;
 * @return {string}
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.prototype.getResponseJsonStr = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotLlmResponse} returns this
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.prototype.setResponseJsonStr = function (value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.repeatedFields_ = [1];

if (jspb.Message.GENERATE_TO_OBJECT) {
  /**
   * Creates an object representation of this proto.
   * Field names that are reserved in JavaScript and will be renamed to pb_name.
   * Optional fields that are not set will be set to undefined.
   * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
   * For the list of reserved names please see:
   *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
   * @param {boolean=} opt_includeInstance Deprecated. whether to include the
   *     JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @return {!Object}
   */
  proto.kwaipilot_llm_api.KwaipilotTokenIds.prototype.toObject = function (opt_includeInstance) {
    return proto.kwaipilot_llm_api.KwaipilotTokenIds.toObject(opt_includeInstance, this);
  };

  /**
   * Static version of the {@see toObject} method.
   * @param {boolean|undefined} includeInstance Deprecated. Whether to include
   *     the JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @param {!proto.kwaipilot_llm_api.KwaipilotTokenIds} msg The msg instance to transform.
   * @return {!Object}
   * @suppress {unusedLocalVariables} f is only used for nested messages
   */
  proto.kwaipilot_llm_api.KwaipilotTokenIds.toObject = function (includeInstance, msg) {
    var f,
      obj = {
        tokenIdsList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f
      };

    if (includeInstance) {
      obj.$jspbMessageInstance = msg;
    }
    return obj;
  };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.KwaipilotTokenIds}
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.deserializeBinary = function (bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.KwaipilotTokenIds();
  return proto.kwaipilot_llm_api.KwaipilotTokenIds.deserializeBinaryFromReader(msg, reader);
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.KwaipilotTokenIds} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.KwaipilotTokenIds}
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.deserializeBinaryFromReader = function (msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
      case 1:
        var values = /** @type {!Array<number>} */ (
          reader.isDelimited() ? reader.readPackedInt32() : [reader.readInt32()]
        );
        for (var i = 0; i < values.length; i++) {
          msg.addTokenIds(values[i]);
        }
        break;
      default:
        reader.skipField();
        break;
    }
  }
  return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.prototype.serializeBinary = function () {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.KwaipilotTokenIds.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.KwaipilotTokenIds} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.serializeBinaryToWriter = function (message, writer) {
  var f = undefined;
  f = message.getTokenIdsList();
  if (f.length > 0) {
    writer.writePackedInt32(1, f);
  }
};

/**
 * repeated int32 token_ids = 1;
 * @return {!Array<number>}
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.prototype.getTokenIdsList = function () {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedField(this, 1));
};

/**
 * @param {!Array<number>} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotTokenIds} returns this
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.prototype.setTokenIdsList = function (value) {
  return jspb.Message.setField(this, 1, value || []);
};

/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_llm_api.KwaipilotTokenIds} returns this
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.prototype.addTokenIds = function (value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};

/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_llm_api.KwaipilotTokenIds} returns this
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.prototype.clearTokenIdsList = function () {
  return this.setTokenIdsList([]);
};

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.repeatedFields_ = [1];

if (jspb.Message.GENERATE_TO_OBJECT) {
  /**
   * Creates an object representation of this proto.
   * Field names that are reserved in JavaScript and will be renamed to pb_name.
   * Optional fields that are not set will be set to undefined.
   * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
   * For the list of reserved names please see:
   *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
   * @param {boolean=} opt_includeInstance Deprecated. whether to include the
   *     JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @return {!Object}
   */
  proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.prototype.toObject = function (
    opt_includeInstance
  ) {
    return proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.toObject(opt_includeInstance, this);
  };

  /**
   * Static version of the {@see toObject} method.
   * @param {boolean|undefined} includeInstance Deprecated. Whether to include
   *     the JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @param {!proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest} msg The msg instance to transform.
   * @return {!Object}
   * @suppress {unusedLocalVariables} f is only used for nested messages
   */
  proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.toObject = function (includeInstance, msg) {
    var f,
      obj = {
        textsList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f
      };

    if (includeInstance) {
      obj.$jspbMessageInstance = msg;
    }
    return obj;
  };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.deserializeBinary = function (bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest();
  return proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.deserializeBinaryFromReader(msg, reader);
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.deserializeBinaryFromReader = function (
  msg,
  reader
) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
      case 1:
        var value = /** @type {string} */ (reader.readString());
        msg.addTexts(value);
        break;
      default:
        reader.skipField();
        break;
    }
  }
  return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.prototype.serializeBinary = function () {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.serializeBinaryToWriter = function (
  message,
  writer
) {
  var f = undefined;
  f = message.getTextsList();
  if (f.length > 0) {
    writer.writeRepeatedString(1, f);
  }
};

/**
 * repeated string texts = 1;
 * @return {!Array<string>}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.prototype.getTextsList = function () {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 1));
};

/**
 * @param {!Array<string>} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.prototype.setTextsList = function (value) {
  return jspb.Message.setField(this, 1, value || []);
};

/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.prototype.addTexts = function (value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};

/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.prototype.clearTextsList = function () {
  return this.setTextsList([]);
};

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot_llm_api.Embedding.repeatedFields_ = [1];

if (jspb.Message.GENERATE_TO_OBJECT) {
  /**
   * Creates an object representation of this proto.
   * Field names that are reserved in JavaScript and will be renamed to pb_name.
   * Optional fields that are not set will be set to undefined.
   * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
   * For the list of reserved names please see:
   *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
   * @param {boolean=} opt_includeInstance Deprecated. whether to include the
   *     JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @return {!Object}
   */
  proto.kwaipilot_llm_api.Embedding.prototype.toObject = function (opt_includeInstance) {
    return proto.kwaipilot_llm_api.Embedding.toObject(opt_includeInstance, this);
  };

  /**
   * Static version of the {@see toObject} method.
   * @param {boolean|undefined} includeInstance Deprecated. Whether to include
   *     the JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @param {!proto.kwaipilot_llm_api.Embedding} msg The msg instance to transform.
   * @return {!Object}
   * @suppress {unusedLocalVariables} f is only used for nested messages
   */
  proto.kwaipilot_llm_api.Embedding.toObject = function (includeInstance, msg) {
    var f,
      obj = {
        embeddingList:
          (f = jspb.Message.getRepeatedFloatingPointField(msg, 1)) == null ? undefined : f
      };

    if (includeInstance) {
      obj.$jspbMessageInstance = msg;
    }
    return obj;
  };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.Embedding}
 */
proto.kwaipilot_llm_api.Embedding.deserializeBinary = function (bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.Embedding();
  return proto.kwaipilot_llm_api.Embedding.deserializeBinaryFromReader(msg, reader);
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.Embedding} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.Embedding}
 */
proto.kwaipilot_llm_api.Embedding.deserializeBinaryFromReader = function (msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
      case 1:
        var values = /** @type {!Array<number>} */ (
          reader.isDelimited() ? reader.readPackedFloat() : [reader.readFloat()]
        );
        for (var i = 0; i < values.length; i++) {
          msg.addEmbedding(values[i]);
        }
        break;
      default:
        reader.skipField();
        break;
    }
  }
  return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.Embedding.prototype.serializeBinary = function () {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.Embedding.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.Embedding} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.Embedding.serializeBinaryToWriter = function (message, writer) {
  var f = undefined;
  f = message.getEmbeddingList();
  if (f.length > 0) {
    writer.writePackedFloat(1, f);
  }
};

/**
 * repeated float embedding = 1;
 * @return {!Array<number>}
 */
proto.kwaipilot_llm_api.Embedding.prototype.getEmbeddingList = function () {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedFloatingPointField(this, 1));
};

/**
 * @param {!Array<number>} value
 * @return {!proto.kwaipilot_llm_api.Embedding} returns this
 */
proto.kwaipilot_llm_api.Embedding.prototype.setEmbeddingList = function (value) {
  return jspb.Message.setField(this, 1, value || []);
};

/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_llm_api.Embedding} returns this
 */
proto.kwaipilot_llm_api.Embedding.prototype.addEmbedding = function (value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};

/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_llm_api.Embedding} returns this
 */
proto.kwaipilot_llm_api.Embedding.prototype.clearEmbeddingList = function () {
  return this.setEmbeddingList([]);
};

if (jspb.Message.GENERATE_TO_OBJECT) {
  /**
   * Creates an object representation of this proto.
   * Field names that are reserved in JavaScript and will be renamed to pb_name.
   * Optional fields that are not set will be set to undefined.
   * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
   * For the list of reserved names please see:
   *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
   * @param {boolean=} opt_includeInstance Deprecated. whether to include the
   *     JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @return {!Object}
   */
  proto.kwaipilot_llm_api.EmbeddingResult.prototype.toObject = function (opt_includeInstance) {
    return proto.kwaipilot_llm_api.EmbeddingResult.toObject(opt_includeInstance, this);
  };

  /**
   * Static version of the {@see toObject} method.
   * @param {boolean|undefined} includeInstance Deprecated. Whether to include
   *     the JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @param {!proto.kwaipilot_llm_api.EmbeddingResult} msg The msg instance to transform.
   * @return {!Object}
   * @suppress {unusedLocalVariables} f is only used for nested messages
   */
  proto.kwaipilot_llm_api.EmbeddingResult.toObject = function (includeInstance, msg) {
    var f,
      obj = {
        embeddingResultMap: (f = msg.getEmbeddingResultMap())
          ? f.toObject(includeInstance, proto.kwaipilot_llm_api.Embedding.toObject)
          : []
      };

    if (includeInstance) {
      obj.$jspbMessageInstance = msg;
    }
    return obj;
  };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.EmbeddingResult}
 */
proto.kwaipilot_llm_api.EmbeddingResult.deserializeBinary = function (bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.EmbeddingResult();
  return proto.kwaipilot_llm_api.EmbeddingResult.deserializeBinaryFromReader(msg, reader);
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.EmbeddingResult} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.EmbeddingResult}
 */
proto.kwaipilot_llm_api.EmbeddingResult.deserializeBinaryFromReader = function (msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
      case 1:
        var value = msg.getEmbeddingResultMap();
        reader.readMessage(value, function (message, reader) {
          jspb.Map.deserializeBinary(
            message,
            reader,
            jspb.BinaryReader.prototype.readString,
            jspb.BinaryReader.prototype.readMessage,
            proto.kwaipilot_llm_api.Embedding.deserializeBinaryFromReader,
            '',
            new proto.kwaipilot_llm_api.Embedding()
          );
        });
        break;
      default:
        reader.skipField();
        break;
    }
  }
  return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.EmbeddingResult.prototype.serializeBinary = function () {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.EmbeddingResult.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.EmbeddingResult} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.EmbeddingResult.serializeBinaryToWriter = function (message, writer) {
  var f = undefined;
  f = message.getEmbeddingResultMap(true);
  if (f && f.getLength() > 0) {
    f.serializeBinary(
      1,
      writer,
      jspb.BinaryWriter.prototype.writeString,
      jspb.BinaryWriter.prototype.writeMessage,
      proto.kwaipilot_llm_api.Embedding.serializeBinaryToWriter
    );
  }
};

/**
 * map<string, Embedding> embedding_result = 1;
 * @param {boolean=} opt_noLazyCreate Do not create the map if
 * empty, instead returning `undefined`
 * @return {!jspb.Map<string,!proto.kwaipilot_llm_api.Embedding>}
 */
proto.kwaipilot_llm_api.EmbeddingResult.prototype.getEmbeddingResultMap = function (
  opt_noLazyCreate
) {
  return /** @type {!jspb.Map<string,!proto.kwaipilot_llm_api.Embedding>} */ (
    jspb.Message.getMapField(this, 1, opt_noLazyCreate, proto.kwaipilot_llm_api.Embedding)
  );
};

/**
 * Clears values from the map. The map will be non-null.
 * @return {!proto.kwaipilot_llm_api.EmbeddingResult} returns this
 */
proto.kwaipilot_llm_api.EmbeddingResult.prototype.clearEmbeddingResultMap = function () {
  this.getEmbeddingResultMap().clear();
  return this;
};

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.repeatedFields_ = [1];

if (jspb.Message.GENERATE_TO_OBJECT) {
  /**
   * Creates an object representation of this proto.
   * Field names that are reserved in JavaScript and will be renamed to pb_name.
   * Optional fields that are not set will be set to undefined.
   * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
   * For the list of reserved names please see:
   *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
   * @param {boolean=} opt_includeInstance Deprecated. whether to include the
   *     JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @return {!Object}
   */
  proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.prototype.toObject = function (
    opt_includeInstance
  ) {
    return proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.toObject(opt_includeInstance, this);
  };

  /**
   * Static version of the {@see toObject} method.
   * @param {boolean|undefined} includeInstance Deprecated. Whether to include
   *     the JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @param {!proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse} msg The msg instance to transform.
   * @return {!Object}
   * @suppress {unusedLocalVariables} f is only used for nested messages
   */
  proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.toObject = function (includeInstance, msg) {
    var f,
      obj = {
        embeddingResultsList: jspb.Message.toObjectList(
          msg.getEmbeddingResultsList(),
          proto.kwaipilot_llm_api.EmbeddingResult.toObject,
          includeInstance
        )
      };

    if (includeInstance) {
      obj.$jspbMessageInstance = msg;
    }
    return obj;
  };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.deserializeBinary = function (bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse();
  return proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.deserializeBinaryFromReader(
    msg,
    reader
  );
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.deserializeBinaryFromReader = function (
  msg,
  reader
) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
      case 1:
        var value = new proto.kwaipilot_llm_api.EmbeddingResult();
        reader.readMessage(
          value,
          proto.kwaipilot_llm_api.EmbeddingResult.deserializeBinaryFromReader
        );
        msg.addEmbeddingResults(value);
        break;
      default:
        reader.skipField();
        break;
    }
  }
  return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.prototype.serializeBinary = function () {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.serializeBinaryToWriter = function (
  message,
  writer
) {
  var f = undefined;
  f = message.getEmbeddingResultsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.kwaipilot_llm_api.EmbeddingResult.serializeBinaryToWriter
    );
  }
};

/**
 * repeated EmbeddingResult embedding_results = 1;
 * @return {!Array<!proto.kwaipilot_llm_api.EmbeddingResult>}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.prototype.getEmbeddingResultsList = function () {
  return /** @type{!Array<!proto.kwaipilot_llm_api.EmbeddingResult>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot_llm_api.EmbeddingResult, 1)
  );
};

/**
 * @param {!Array<!proto.kwaipilot_llm_api.EmbeddingResult>} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse} returns this
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.prototype.setEmbeddingResultsList = function (
  value
) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};

/**
 * @param {!proto.kwaipilot_llm_api.EmbeddingResult=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_llm_api.EmbeddingResult}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.prototype.addEmbeddingResults = function (
  opt_value,
  opt_index
) {
  return jspb.Message.addToRepeatedWrapperField(
    this,
    1,
    opt_value,
    proto.kwaipilot_llm_api.EmbeddingResult,
    opt_index
  );
};

/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse} returns this
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.prototype.clearEmbeddingResultsList =
  function () {
    return this.setEmbeddingResultsList([]);
  };

goog.object.extend(exports, proto.kwaipilot_llm_api);
