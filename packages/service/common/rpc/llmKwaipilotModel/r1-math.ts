import { RPC, GRPCProtocol, KessRegistry, Consumer } from '@infra-node/rpc';
import { IConsumer } from './proto/functioncall_kwaipilot_grpc_pb';
import { TIMEOUT } from './const';

const registry = new KessRegistry({
  balance: 'round-robin',
  serviceName: 'grpc_python_kwaipilot_llm_api_reason'
});

const protocol = new GRPCProtocol({
  methodLowerCamelCase: true,
  protoLoader: {
    enums: Number,
    oneofs: false
  },
  proto: require('./proto/functioncall_kwaipilot_grpc_pb')
});

let consumer: Consumer<IConsumer> | null = null;
export async function getLLMModelConsumer() {
  if (!consumer) {
    consumer = await RPC.createConsumer<IConsumer>({
      registry,
      protocol,
      timeout: TIMEOUT
    });
  }
  return consumer;
}

export * from './proto/functioncall_kwaipilot_pb';
