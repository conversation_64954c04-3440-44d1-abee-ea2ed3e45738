import { RPC, GRPCProtocol, KessRegistry, Consumer, Metadata } from '@infra-node/rpc';
import { IConsumer } from './proto/llm_server_grpc_pb';
import { IS_KWS_PREONLINE, KWS_LANE_ID } from '@fastgpt/global/core/chat/constants';
import { createLogger } from '@fastgpt/global/common/util/logger';

const logger = createLogger('llm-service');
const TIMEOUT = 5_000;

const GRPC_JAVA_SERVICE = 'pmo-llm-server-api.llm-kwaipilot-service';

logger.info(`current env: ${IS_KWS_PREONLINE ? 'pre' : 'prod'}, service: ${GRPC_JAVA_SERVICE}`);

const registry = new KessRegistry({
  balance: 'round-robin',
  serviceName: GRPC_JAVA_SERVICE
});

const protocol = new GRPCProtocol({
  methodLowerCamelCase: true,
  protoLoader: {
    enums: Number,
    oneofs: false
  },
  proto: require('./proto/llm_server_grpc_pb')
});

let consumer: Consumer<IConsumer> | null = null;
export async function getLLMServiceConsumer() {
  if (!consumer) {
    consumer = await RPC.createConsumer<IConsumer>({
      registry,
      protocol,
      timeout: TIMEOUT
    });
  }
  return consumer;
}

export function getMetadata() {
  const metadata = new Metadata();
  if (KWS_LANE_ID) {
    metadata.set('lane_id', KWS_LANE_ID);
    logger.info(`set lane_id: ${KWS_LANE_ID}`);
  }
  return metadata;
}

export * from './proto/llm_server_pb';
