syntax = "proto3";

package kwaipilot;
option java_multiple_files = true;

message ChatModelListQuery {
  string username = 1;
}

message ChatModel {
  string code = 1;
  string name = 2;
  string desc = 3;
  string icon = 4;
  bool disable = 5;
  int32 max_input_length = 6;
  string disable_icon = 7;
  int32 max_input_tokens = 8;
  int32 max_output_tokens = 9;
  bool support_img = 10;
}

message UserInfo {
  string username = 1;
  string avatar_url = 2;
  string name = 3;
  string dept1 = 4;
  string dept_code = 5;
}

message ChatModelListResponse {
  repeated ChatModel chat_models = 1;
}

message AgentModelListQuery {
  int32 limit = 1;
  int32 offset = 2;
  string name = 3;
  int32 order_type = 4;
  string username = 5;
}

message AgentModel {
  string auid = 1;
  int64 create_time = 2;
  string description = 3;
  string icon = 4;
  int64 id = 5;
  bool locked = 6;
  repeated UserInfo maintainer_list = 7;
  string name = 8;
  bool official = 9;
  int64 parent_agent_id = 10;
  int64 ref_count = 11;
  string role_description = 12;
  string status = 13;
  int64 update_time = 14;
  UserInfo updater = 15;
  string visibility = 16;
}

message AgentModelListResponse {
  repeated AgentModel list = 1;
  int64 total = 2;
  int32 page_num = 3;
  int32 page_size = 4;
  int32 pages = 5;
  bool has_more = 6;
}

message ChatMessage {
  string role = 1; // user or assistant
  string content = 2;
}

message Knowledge {
  string content = 1;
  string link = 2;
  string title = 3;
  string knowledge_repo_id = 4;
  string knowledge_repo_name = 5;
  float score = 6;
  string tag = 7; // 知识标签
}

message KnowledgeRepoQuery {
  string username = 1;
  int32 page_size = 2;
  int32 page_num = 3;
  string name = 4;
}

message KnowledgeRepoResp {
  int64 id = 1;
  string name = 2;
  string description = 3;
  string icon = 4;
}

message KnowledgeRepoListResp {
  repeated KnowledgeRepoResp list = 1;
  bool has_more = 2;
  int32 page_num = 3;
  int32 page_size = 4;
  int32 pages = 5;
  int64 total = 6;
}

message ToolDetailRequest {
  int64 tool_id = 1;
  string tuid = 2;
  string username = 3;
}

message ToolParamsSchema {
  string body_params_schema = 1;
  string query_params_schema = 2;
  string header_params_schema = 3;
  string path_params_schema = 4;
}

message ToolAuthConfig {
  int32 auth_type = 1;
  string location = 2;
  string name = 3;
  string value = 4;
  string app_key = 5;
  string secret_key = 6;
}

message ToolDetailResponse {
  int64 id = 1;
  string tuid = 2;
  string name = 3;
  string icon = 4;
  string description = 5;
  string code = 6;
  string api_method = 7;
  string api_path = 8;
  ToolParamsSchema enter_params_schema = 9;
  string out_param_schema = 10;
  ToolAuthConfig auth_config = 11;
}

message ChatFile {
  string code = 1; // 代码内容
  string name = 2; // 文件名
  string language = 3; // 编程语言
}

message CodeSearchData {
  int64 id = 1;
  string path = 2;
  int64 start_line_no = 3;
  int64 end_line_no = 4;
  int64 start_col_no = 5;
  int64 end_col_no = 6;
  string code = 7;
  string language = 8;
  string function_name = 9;
  string function_signature = 10;
  string code_type = 11;
}

message ServerKnowledgeBatchCreateRequest {
  int64 repo_id = 1;                    // 知识库ID
  repeated ServerKnowledgeRequest knowledge = 2;              // 知识
  string creator = 3;                   // 创建人
  string token = 4;                     // AccessToken
}

message ServerKnowledgeRequest {
  int32 source_type = 1;                // 知识来源类型（0 - Docs / 1 - 本地文件 / 2 - Techlink文档 / 3 - 多网页知识）
  string source_url = 2;                // 知识链接
}

message ServerKnowledgeResp {
  int64 id = 1;                         // 知识ID
  string name = 2;                      // 知识名称
  int64 repo_id = 3;                    // 知识库ID
  int32 source_type = 4;                // 知识来源类型（0 - Docs / 1 - 本地文件 / 2 - Techlink文档 / 3 - 多网页知识）
  string source_url = 5;                // 知识链接
  int64 update_time = 6;                // 修改时间
  string creator = 7;                   // 创建人
  int32 status = 8;                     // 发布状态（0 - 未发布 / 1 - 已发布）
}

message ServerKnowledgeBatchResponse {
  repeated ServerKnowledgeResp knowledge = 1;
}

message ServerKnowledgeDeleteRequest {
  int64 id = 1;                          // 知识ID
  string username = 2;                   // 操作人
  string token = 3;                      // AccessToken
  int64 repo_id = 4;                     // 知识库ID
}

message ServerKnowledgeDeleteResponse {
  bool success = 1;
}

message UserTokenQueryResponse {
  int64 id = 1;
  string token = 2;
  string username = 3;
  int64 create_time = 4;
  int64 update_time = 5;
}

message UserTokenQueryRequest {
  string username = 1;
}

message UserTokenCheckRequest {
  string token = 1;
}

message UserTokenCheckResponse {
  string username = 1;
}

service LlmServerService {
  rpc ListChatModels (ChatModelListQuery) returns (ChatModelListResponse) {}
  rpc ListAgents(AgentModelListQuery) returns (AgentModelListResponse) {}
  rpc GetToolDetail(ToolDetailRequest) returns (ToolDetailResponse);
  rpc KnowledgeRepoList(KnowledgeRepoQuery) returns (KnowledgeRepoListResp); // 知识库列表
  rpc ServerKnowledgeBatchCreate(ServerKnowledgeBatchCreateRequest) returns (ServerKnowledgeBatchResponse); // 知识库批量创建知识
  rpc ServerKnowledgeDelete(ServerKnowledgeDeleteRequest) returns (ServerKnowledgeDeleteResponse); // 知识库删除知识
  rpc GetUserToken(UserTokenQueryRequest) returns (UserTokenQueryResponse); // 获取用户token
  rpc CheckToken(UserTokenCheckRequest) returns (UserTokenCheckResponse); // 校验 token
}
