// package: kwaipilot
// file: llm_server.proto

/* tslint:disable */
/* eslint-disable */

import * as grpc from '@grpc/grpc-js';
import * as llm_server_pb from './llm_server_pb';
import type { Metadata, RPCCallOptions } from '@infra-node/rpc';

export interface ILlmServerServiceClient {
  listChatModels(
    request: llm_server_pb.ChatModelListQuery,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ChatModelListResponse
    ) => void
  ): void;
  listChatModels(
    request: llm_server_pb.ChatModelListQuery,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ChatModelListResponse
    ) => void
  ): void;
  listChatModels(
    request: llm_server_pb.ChatModelListQuery,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ChatModelListResponse
    ) => void
  ): void;
  listAgents(
    request: llm_server_pb.AgentModelListQuery,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.AgentModelListResponse
    ) => void
  ): void;
  listAgents(
    request: llm_server_pb.AgentModelListQuery,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.AgentModelListResponse
    ) => void
  ): void;
  listAgents(
    request: llm_server_pb.AgentModelListQuery,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.AgentModelListResponse
    ) => void
  ): void;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest,
    callback: (error: grpc.ServiceError | null, response: llm_server_pb.ToolDetailResponse) => void
  ): void;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest,
    metadata: Metadata,
    callback: (error: grpc.ServiceError | null, response: llm_server_pb.ToolDetailResponse) => void
  ): void;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (error: grpc.ServiceError | null, response: llm_server_pb.ToolDetailResponse) => void
  ): void;
  knowledgeRepoList(
    request: llm_server_pb.KnowledgeRepoQuery,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.KnowledgeRepoListResp
    ) => void
  ): void;
  knowledgeRepoList(
    request: llm_server_pb.KnowledgeRepoQuery,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.KnowledgeRepoListResp
    ) => void
  ): void;
  knowledgeRepoList(
    request: llm_server_pb.KnowledgeRepoQuery,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.KnowledgeRepoListResp
    ) => void
  ): void;
  serverKnowledgeBatchCreate(
    request: llm_server_pb.ServerKnowledgeBatchCreateRequest,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ServerKnowledgeBatchResponse
    ) => void
  ): void;
  serverKnowledgeBatchCreate(
    request: llm_server_pb.ServerKnowledgeBatchCreateRequest,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ServerKnowledgeBatchResponse
    ) => void
  ): void;
  serverKnowledgeBatchCreate(
    request: llm_server_pb.ServerKnowledgeBatchCreateRequest,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ServerKnowledgeBatchResponse
    ) => void
  ): void;
  serverKnowledgeDelete(
    request: llm_server_pb.ServerKnowledgeDeleteRequest,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ServerKnowledgeDeleteResponse
    ) => void
  ): void;
  serverKnowledgeDelete(
    request: llm_server_pb.ServerKnowledgeDeleteRequest,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ServerKnowledgeDeleteResponse
    ) => void
  ): void;
  serverKnowledgeDelete(
    request: llm_server_pb.ServerKnowledgeDeleteRequest,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ServerKnowledgeDeleteResponse
    ) => void
  ): void;
  getUserToken(
    request: llm_server_pb.UserTokenQueryRequest,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.UserTokenQueryResponse
    ) => void
  ): void;
  getUserToken(
    request: llm_server_pb.UserTokenQueryRequest,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.UserTokenQueryResponse
    ) => void
  ): void;
  getUserToken(
    request: llm_server_pb.UserTokenQueryRequest,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.UserTokenQueryResponse
    ) => void
  ): void;
  checkToken(
    request: llm_server_pb.UserTokenCheckRequest,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.UserTokenCheckResponse
    ) => void
  ): void;
  checkToken(
    request: llm_server_pb.UserTokenCheckRequest,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.UserTokenCheckResponse
    ) => void
  ): void;
  checkToken(
    request: llm_server_pb.UserTokenCheckRequest,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.UserTokenCheckResponse
    ) => void
  ): void;
}

export interface ILlmServerServiceClientPromisify {
  listChatModels(
    request: llm_server_pb.ChatModelListQuery
  ): Promise<llm_server_pb.ChatModelListResponse>;
  listChatModels(
    request: llm_server_pb.ChatModelListQuery,
    metadata: Metadata
  ): Promise<llm_server_pb.ChatModelListResponse>;
  listChatModels(
    request: llm_server_pb.ChatModelListQuery,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.ChatModelListResponse>;
  listAgents(
    request: llm_server_pb.AgentModelListQuery
  ): Promise<llm_server_pb.AgentModelListResponse>;
  listAgents(
    request: llm_server_pb.AgentModelListQuery,
    metadata: Metadata
  ): Promise<llm_server_pb.AgentModelListResponse>;
  listAgents(
    request: llm_server_pb.AgentModelListQuery,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.AgentModelListResponse>;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest
  ): Promise<llm_server_pb.ToolDetailResponse>;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest,
    metadata: Metadata
  ): Promise<llm_server_pb.ToolDetailResponse>;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.ToolDetailResponse>;
  knowledgeRepoList(
    request: llm_server_pb.KnowledgeRepoQuery
  ): Promise<llm_server_pb.KnowledgeRepoListResp>;
  knowledgeRepoList(
    request: llm_server_pb.KnowledgeRepoQuery,
    metadata: Metadata
  ): Promise<llm_server_pb.KnowledgeRepoListResp>;
  knowledgeRepoList(
    request: llm_server_pb.KnowledgeRepoQuery,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.KnowledgeRepoListResp>;
  serverKnowledgeBatchCreate(
    request: llm_server_pb.ServerKnowledgeBatchCreateRequest
  ): Promise<llm_server_pb.ServerKnowledgeBatchResponse>;
  serverKnowledgeBatchCreate(
    request: llm_server_pb.ServerKnowledgeBatchCreateRequest,
    metadata: Metadata
  ): Promise<llm_server_pb.ServerKnowledgeBatchResponse>;
  serverKnowledgeBatchCreate(
    request: llm_server_pb.ServerKnowledgeBatchCreateRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.ServerKnowledgeBatchResponse>;
  serverKnowledgeDelete(
    request: llm_server_pb.ServerKnowledgeDeleteRequest
  ): Promise<llm_server_pb.ServerKnowledgeDeleteResponse>;
  serverKnowledgeDelete(
    request: llm_server_pb.ServerKnowledgeDeleteRequest,
    metadata: Metadata
  ): Promise<llm_server_pb.ServerKnowledgeDeleteResponse>;
  serverKnowledgeDelete(
    request: llm_server_pb.ServerKnowledgeDeleteRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.ServerKnowledgeDeleteResponse>;
  getUserToken(
    request: llm_server_pb.UserTokenQueryRequest
  ): Promise<llm_server_pb.UserTokenQueryResponse>;
  getUserToken(
    request: llm_server_pb.UserTokenQueryRequest,
    metadata: Metadata
  ): Promise<llm_server_pb.UserTokenQueryResponse>;
  getUserToken(
    request: llm_server_pb.UserTokenQueryRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.UserTokenQueryResponse>;
  checkToken(
    request: llm_server_pb.UserTokenCheckRequest
  ): Promise<llm_server_pb.UserTokenCheckResponse>;
  checkToken(
    request: llm_server_pb.UserTokenCheckRequest,
    metadata: Metadata
  ): Promise<llm_server_pb.UserTokenCheckResponse>;
  checkToken(
    request: llm_server_pb.UserTokenCheckRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.UserTokenCheckResponse>;
}

export interface ILlmServerServiceClientDynamic {
  listChatModels(
    request: llm_server_pb.ChatModelListQuery.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ChatModelListResponse.DynamicObject
    ) => void
  ): void;
  listChatModels(
    request: llm_server_pb.ChatModelListQuery.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ChatModelListResponse.DynamicObject
    ) => void
  ): void;
  listChatModels(
    request: llm_server_pb.ChatModelListQuery.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ChatModelListResponse.DynamicObject
    ) => void
  ): void;
  listAgents(
    request: llm_server_pb.AgentModelListQuery.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.AgentModelListResponse.DynamicObject
    ) => void
  ): void;
  listAgents(
    request: llm_server_pb.AgentModelListQuery.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.AgentModelListResponse.DynamicObject
    ) => void
  ): void;
  listAgents(
    request: llm_server_pb.AgentModelListQuery.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.AgentModelListResponse.DynamicObject
    ) => void
  ): void;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ToolDetailResponse.DynamicObject
    ) => void
  ): void;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ToolDetailResponse.DynamicObject
    ) => void
  ): void;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ToolDetailResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoList(
    request: llm_server_pb.KnowledgeRepoQuery.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.KnowledgeRepoListResp.DynamicObject
    ) => void
  ): void;
  knowledgeRepoList(
    request: llm_server_pb.KnowledgeRepoQuery.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.KnowledgeRepoListResp.DynamicObject
    ) => void
  ): void;
  knowledgeRepoList(
    request: llm_server_pb.KnowledgeRepoQuery.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.KnowledgeRepoListResp.DynamicObject
    ) => void
  ): void;
  serverKnowledgeBatchCreate(
    request: llm_server_pb.ServerKnowledgeBatchCreateRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ServerKnowledgeBatchResponse.DynamicObject
    ) => void
  ): void;
  serverKnowledgeBatchCreate(
    request: llm_server_pb.ServerKnowledgeBatchCreateRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ServerKnowledgeBatchResponse.DynamicObject
    ) => void
  ): void;
  serverKnowledgeBatchCreate(
    request: llm_server_pb.ServerKnowledgeBatchCreateRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ServerKnowledgeBatchResponse.DynamicObject
    ) => void
  ): void;
  serverKnowledgeDelete(
    request: llm_server_pb.ServerKnowledgeDeleteRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ServerKnowledgeDeleteResponse.DynamicObject
    ) => void
  ): void;
  serverKnowledgeDelete(
    request: llm_server_pb.ServerKnowledgeDeleteRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ServerKnowledgeDeleteResponse.DynamicObject
    ) => void
  ): void;
  serverKnowledgeDelete(
    request: llm_server_pb.ServerKnowledgeDeleteRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ServerKnowledgeDeleteResponse.DynamicObject
    ) => void
  ): void;
  getUserToken(
    request: llm_server_pb.UserTokenQueryRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.UserTokenQueryResponse.DynamicObject
    ) => void
  ): void;
  getUserToken(
    request: llm_server_pb.UserTokenQueryRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.UserTokenQueryResponse.DynamicObject
    ) => void
  ): void;
  getUserToken(
    request: llm_server_pb.UserTokenQueryRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.UserTokenQueryResponse.DynamicObject
    ) => void
  ): void;
  checkToken(
    request: llm_server_pb.UserTokenCheckRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.UserTokenCheckResponse.DynamicObject
    ) => void
  ): void;
  checkToken(
    request: llm_server_pb.UserTokenCheckRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.UserTokenCheckResponse.DynamicObject
    ) => void
  ): void;
  checkToken(
    request: llm_server_pb.UserTokenCheckRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.UserTokenCheckResponse.DynamicObject
    ) => void
  ): void;
}

export interface ILlmServerServiceClientDynamicPromisify {
  listChatModels(
    request: llm_server_pb.ChatModelListQuery.DynamicObject
  ): Promise<llm_server_pb.ChatModelListResponse.DynamicObject>;
  listChatModels(
    request: llm_server_pb.ChatModelListQuery.DynamicObject,
    metadata: Metadata
  ): Promise<llm_server_pb.ChatModelListResponse.DynamicObject>;
  listChatModels(
    request: llm_server_pb.ChatModelListQuery.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.ChatModelListResponse.DynamicObject>;
  listAgents(
    request: llm_server_pb.AgentModelListQuery.DynamicObject
  ): Promise<llm_server_pb.AgentModelListResponse.DynamicObject>;
  listAgents(
    request: llm_server_pb.AgentModelListQuery.DynamicObject,
    metadata: Metadata
  ): Promise<llm_server_pb.AgentModelListResponse.DynamicObject>;
  listAgents(
    request: llm_server_pb.AgentModelListQuery.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.AgentModelListResponse.DynamicObject>;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest.DynamicObject
  ): Promise<llm_server_pb.ToolDetailResponse.DynamicObject>;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest.DynamicObject,
    metadata: Metadata
  ): Promise<llm_server_pb.ToolDetailResponse.DynamicObject>;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.ToolDetailResponse.DynamicObject>;
  knowledgeRepoList(
    request: llm_server_pb.KnowledgeRepoQuery.DynamicObject
  ): Promise<llm_server_pb.KnowledgeRepoListResp.DynamicObject>;
  knowledgeRepoList(
    request: llm_server_pb.KnowledgeRepoQuery.DynamicObject,
    metadata: Metadata
  ): Promise<llm_server_pb.KnowledgeRepoListResp.DynamicObject>;
  knowledgeRepoList(
    request: llm_server_pb.KnowledgeRepoQuery.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.KnowledgeRepoListResp.DynamicObject>;
  serverKnowledgeBatchCreate(
    request: llm_server_pb.ServerKnowledgeBatchCreateRequest.DynamicObject
  ): Promise<llm_server_pb.ServerKnowledgeBatchResponse.DynamicObject>;
  serverKnowledgeBatchCreate(
    request: llm_server_pb.ServerKnowledgeBatchCreateRequest.DynamicObject,
    metadata: Metadata
  ): Promise<llm_server_pb.ServerKnowledgeBatchResponse.DynamicObject>;
  serverKnowledgeBatchCreate(
    request: llm_server_pb.ServerKnowledgeBatchCreateRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.ServerKnowledgeBatchResponse.DynamicObject>;
  serverKnowledgeDelete(
    request: llm_server_pb.ServerKnowledgeDeleteRequest.DynamicObject
  ): Promise<llm_server_pb.ServerKnowledgeDeleteResponse.DynamicObject>;
  serverKnowledgeDelete(
    request: llm_server_pb.ServerKnowledgeDeleteRequest.DynamicObject,
    metadata: Metadata
  ): Promise<llm_server_pb.ServerKnowledgeDeleteResponse.DynamicObject>;
  serverKnowledgeDelete(
    request: llm_server_pb.ServerKnowledgeDeleteRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.ServerKnowledgeDeleteResponse.DynamicObject>;
  getUserToken(
    request: llm_server_pb.UserTokenQueryRequest.DynamicObject
  ): Promise<llm_server_pb.UserTokenQueryResponse.DynamicObject>;
  getUserToken(
    request: llm_server_pb.UserTokenQueryRequest.DynamicObject,
    metadata: Metadata
  ): Promise<llm_server_pb.UserTokenQueryResponse.DynamicObject>;
  getUserToken(
    request: llm_server_pb.UserTokenQueryRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.UserTokenQueryResponse.DynamicObject>;
  checkToken(
    request: llm_server_pb.UserTokenCheckRequest.DynamicObject
  ): Promise<llm_server_pb.UserTokenCheckResponse.DynamicObject>;
  checkToken(
    request: llm_server_pb.UserTokenCheckRequest.DynamicObject,
    metadata: Metadata
  ): Promise<llm_server_pb.UserTokenCheckResponse.DynamicObject>;
  checkToken(
    request: llm_server_pb.UserTokenCheckRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.UserTokenCheckResponse.DynamicObject>;
}

export interface IConsumer {
  LlmServerService: ILlmServerServiceClient;
  __promise__: {
    LlmServerService: ILlmServerServiceClientPromisify;
  };
}

export interface IConsumerDynamic {
  LlmServerService: ILlmServerServiceClientDynamic;
  __promise__: {
    LlmServerService: ILlmServerServiceClientDynamicPromisify;
  };
}
