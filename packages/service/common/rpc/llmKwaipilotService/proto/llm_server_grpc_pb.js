// GENERATED CODE -- DO NOT EDIT!

'use strict';
var grpc = require('@grpc/grpc-js');
var llm_server_pb = require('./llm_server_pb.js');

function serialize_kwaipilot_AgentModelListQuery(arg) {
  if (!(arg instanceof llm_server_pb.AgentModelListQuery)) {
    throw new Error('Expected argument of type kwaipilot.AgentModelListQuery');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_AgentModelListQuery(buffer_arg) {
  return llm_server_pb.AgentModelListQuery.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_AgentModelListResponse(arg) {
  if (!(arg instanceof llm_server_pb.AgentModelListResponse)) {
    throw new Error('Expected argument of type kwaipilot.AgentModelListResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_AgentModelListResponse(buffer_arg) {
  return llm_server_pb.AgentModelListResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_ChatModelListQuery(arg) {
  if (!(arg instanceof llm_server_pb.ChatModelListQuery)) {
    throw new Error('Expected argument of type kwaipilot.ChatModelListQuery');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_ChatModelListQuery(buffer_arg) {
  return llm_server_pb.ChatModelListQuery.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_ChatModelListResponse(arg) {
  if (!(arg instanceof llm_server_pb.ChatModelListResponse)) {
    throw new Error('Expected argument of type kwaipilot.ChatModelListResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_ChatModelListResponse(buffer_arg) {
  return llm_server_pb.ChatModelListResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_KnowledgeRepoListResp(arg) {
  if (!(arg instanceof llm_server_pb.KnowledgeRepoListResp)) {
    throw new Error('Expected argument of type kwaipilot.KnowledgeRepoListResp');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_KnowledgeRepoListResp(buffer_arg) {
  return llm_server_pb.KnowledgeRepoListResp.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_KnowledgeRepoQuery(arg) {
  if (!(arg instanceof llm_server_pb.KnowledgeRepoQuery)) {
    throw new Error('Expected argument of type kwaipilot.KnowledgeRepoQuery');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_KnowledgeRepoQuery(buffer_arg) {
  return llm_server_pb.KnowledgeRepoQuery.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_ServerKnowledgeBatchCreateRequest(arg) {
  if (!(arg instanceof llm_server_pb.ServerKnowledgeBatchCreateRequest)) {
    throw new Error('Expected argument of type kwaipilot.ServerKnowledgeBatchCreateRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_ServerKnowledgeBatchCreateRequest(buffer_arg) {
  return llm_server_pb.ServerKnowledgeBatchCreateRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_ServerKnowledgeBatchResponse(arg) {
  if (!(arg instanceof llm_server_pb.ServerKnowledgeBatchResponse)) {
    throw new Error('Expected argument of type kwaipilot.ServerKnowledgeBatchResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_ServerKnowledgeBatchResponse(buffer_arg) {
  return llm_server_pb.ServerKnowledgeBatchResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_ServerKnowledgeDeleteRequest(arg) {
  if (!(arg instanceof llm_server_pb.ServerKnowledgeDeleteRequest)) {
    throw new Error('Expected argument of type kwaipilot.ServerKnowledgeDeleteRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_ServerKnowledgeDeleteRequest(buffer_arg) {
  return llm_server_pb.ServerKnowledgeDeleteRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_ServerKnowledgeDeleteResponse(arg) {
  if (!(arg instanceof llm_server_pb.ServerKnowledgeDeleteResponse)) {
    throw new Error('Expected argument of type kwaipilot.ServerKnowledgeDeleteResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_ServerKnowledgeDeleteResponse(buffer_arg) {
  return llm_server_pb.ServerKnowledgeDeleteResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_ToolDetailRequest(arg) {
  if (!(arg instanceof llm_server_pb.ToolDetailRequest)) {
    throw new Error('Expected argument of type kwaipilot.ToolDetailRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_ToolDetailRequest(buffer_arg) {
  return llm_server_pb.ToolDetailRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_ToolDetailResponse(arg) {
  if (!(arg instanceof llm_server_pb.ToolDetailResponse)) {
    throw new Error('Expected argument of type kwaipilot.ToolDetailResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_ToolDetailResponse(buffer_arg) {
  return llm_server_pb.ToolDetailResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_UserTokenCheckRequest(arg) {
  if (!(arg instanceof llm_server_pb.UserTokenCheckRequest)) {
    throw new Error('Expected argument of type kwaipilot.UserTokenCheckRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_UserTokenCheckRequest(buffer_arg) {
  return llm_server_pb.UserTokenCheckRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_UserTokenCheckResponse(arg) {
  if (!(arg instanceof llm_server_pb.UserTokenCheckResponse)) {
    throw new Error('Expected argument of type kwaipilot.UserTokenCheckResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_UserTokenCheckResponse(buffer_arg) {
  return llm_server_pb.UserTokenCheckResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_UserTokenQueryRequest(arg) {
  if (!(arg instanceof llm_server_pb.UserTokenQueryRequest)) {
    throw new Error('Expected argument of type kwaipilot.UserTokenQueryRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_UserTokenQueryRequest(buffer_arg) {
  return llm_server_pb.UserTokenQueryRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_UserTokenQueryResponse(arg) {
  if (!(arg instanceof llm_server_pb.UserTokenQueryResponse)) {
    throw new Error('Expected argument of type kwaipilot.UserTokenQueryResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_UserTokenQueryResponse(buffer_arg) {
  return llm_server_pb.UserTokenQueryResponse.deserializeBinary(new Uint8Array(buffer_arg));
}


var LlmServerServiceService = exports.LlmServerServiceService = {
  listChatModels: {
    path: '/kwaipilot.LlmServerService/ListChatModels',
    requestStream: false,
    responseStream: false,
    requestType: llm_server_pb.ChatModelListQuery,
    responseType: llm_server_pb.ChatModelListResponse,
    requestSerialize: serialize_kwaipilot_ChatModelListQuery,
    requestDeserialize: deserialize_kwaipilot_ChatModelListQuery,
    responseSerialize: serialize_kwaipilot_ChatModelListResponse,
    responseDeserialize: deserialize_kwaipilot_ChatModelListResponse,
  },
  listAgents: {
    path: '/kwaipilot.LlmServerService/ListAgents',
    requestStream: false,
    responseStream: false,
    requestType: llm_server_pb.AgentModelListQuery,
    responseType: llm_server_pb.AgentModelListResponse,
    requestSerialize: serialize_kwaipilot_AgentModelListQuery,
    requestDeserialize: deserialize_kwaipilot_AgentModelListQuery,
    responseSerialize: serialize_kwaipilot_AgentModelListResponse,
    responseDeserialize: deserialize_kwaipilot_AgentModelListResponse,
  },
  getToolDetail: {
    path: '/kwaipilot.LlmServerService/GetToolDetail',
    requestStream: false,
    responseStream: false,
    requestType: llm_server_pb.ToolDetailRequest,
    responseType: llm_server_pb.ToolDetailResponse,
    requestSerialize: serialize_kwaipilot_ToolDetailRequest,
    requestDeserialize: deserialize_kwaipilot_ToolDetailRequest,
    responseSerialize: serialize_kwaipilot_ToolDetailResponse,
    responseDeserialize: deserialize_kwaipilot_ToolDetailResponse,
  },
  knowledgeRepoList: {
    path: '/kwaipilot.LlmServerService/KnowledgeRepoList',
    requestStream: false,
    responseStream: false,
    requestType: llm_server_pb.KnowledgeRepoQuery,
    responseType: llm_server_pb.KnowledgeRepoListResp,
    requestSerialize: serialize_kwaipilot_KnowledgeRepoQuery,
    requestDeserialize: deserialize_kwaipilot_KnowledgeRepoQuery,
    responseSerialize: serialize_kwaipilot_KnowledgeRepoListResp,
    responseDeserialize: deserialize_kwaipilot_KnowledgeRepoListResp,
  },
  // 知识库列表
serverKnowledgeBatchCreate: {
    path: '/kwaipilot.LlmServerService/ServerKnowledgeBatchCreate',
    requestStream: false,
    responseStream: false,
    requestType: llm_server_pb.ServerKnowledgeBatchCreateRequest,
    responseType: llm_server_pb.ServerKnowledgeBatchResponse,
    requestSerialize: serialize_kwaipilot_ServerKnowledgeBatchCreateRequest,
    requestDeserialize: deserialize_kwaipilot_ServerKnowledgeBatchCreateRequest,
    responseSerialize: serialize_kwaipilot_ServerKnowledgeBatchResponse,
    responseDeserialize: deserialize_kwaipilot_ServerKnowledgeBatchResponse,
  },
  // 知识库批量创建知识
serverKnowledgeDelete: {
    path: '/kwaipilot.LlmServerService/ServerKnowledgeDelete',
    requestStream: false,
    responseStream: false,
    requestType: llm_server_pb.ServerKnowledgeDeleteRequest,
    responseType: llm_server_pb.ServerKnowledgeDeleteResponse,
    requestSerialize: serialize_kwaipilot_ServerKnowledgeDeleteRequest,
    requestDeserialize: deserialize_kwaipilot_ServerKnowledgeDeleteRequest,
    responseSerialize: serialize_kwaipilot_ServerKnowledgeDeleteResponse,
    responseDeserialize: deserialize_kwaipilot_ServerKnowledgeDeleteResponse,
  },
  // 知识库删除知识
getUserToken: {
    path: '/kwaipilot.LlmServerService/GetUserToken',
    requestStream: false,
    responseStream: false,
    requestType: llm_server_pb.UserTokenQueryRequest,
    responseType: llm_server_pb.UserTokenQueryResponse,
    requestSerialize: serialize_kwaipilot_UserTokenQueryRequest,
    requestDeserialize: deserialize_kwaipilot_UserTokenQueryRequest,
    responseSerialize: serialize_kwaipilot_UserTokenQueryResponse,
    responseDeserialize: deserialize_kwaipilot_UserTokenQueryResponse,
  },
  // 获取用户token
checkToken: {
    path: '/kwaipilot.LlmServerService/CheckToken',
    requestStream: false,
    responseStream: false,
    requestType: llm_server_pb.UserTokenCheckRequest,
    responseType: llm_server_pb.UserTokenCheckResponse,
    requestSerialize: serialize_kwaipilot_UserTokenCheckRequest,
    requestDeserialize: deserialize_kwaipilot_UserTokenCheckRequest,
    responseSerialize: serialize_kwaipilot_UserTokenCheckResponse,
    responseDeserialize: deserialize_kwaipilot_UserTokenCheckResponse,
  },
  // 校验 token
};

exports.LlmServerServiceClient = grpc.makeGenericClientConstructor(LlmServerServiceService);
