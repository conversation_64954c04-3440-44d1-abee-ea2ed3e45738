import { RPC, GRPCProtocol, KessRegistry, Consumer, Metadata } from '@infra-node/rpc';
import { IConsumer } from './proto/llm_rag_server_grpc_pb';
import { KWS_LANE_ID } from '@fastgpt/global/core/chat/constants';
import { createLogger } from '@fastgpt/global/common/util/logger';

const logger = createLogger('llm-service');
const TIMEOUT = 20_000;

const GRPC_JAVA_SERVICE = 'pmo-llm-server-api.llm-kwaipilot-rag-service';

logger.info(`service: ${GRPC_JAVA_SERVICE}`);

const registry = new KessRegistry({
  balance: 'round-robin',
  serviceName: GRPC_JAVA_SERVICE
});

const protocol = new GRPCProtocol({
  methodLowerCamelCase: true,
  protoLoader: {
    enums: Number,
    oneofs: false
  },
  proto: require('./proto/llm_rag_server_grpc_pb')
});

let consumer: Consumer<IConsumer> | null = null;
export async function getLLMRagServiceConsumer() {
  if (!consumer) {
    consumer = await RPC.createConsumer<IConsumer>({
      registry,
      protocol,
      timeout: TIMEOUT
    });
  }
  return consumer;
}

export function getMetadata() {
  const metadata = new Metadata();
  if (KWS_LANE_ID) {
    metadata.set('lane_id', KWS_LANE_ID);
    logger.info(`set lane_id: ${KWS_LANE_ID}`);
  }
  return metadata;
}

export * from './proto/llm_rag_server_pb';
