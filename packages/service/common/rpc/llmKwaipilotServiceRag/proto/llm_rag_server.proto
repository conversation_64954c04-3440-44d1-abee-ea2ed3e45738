syntax = "proto3";

package kwaipilot_rag;
option java_multiple_files = true;

message KnowledgeRepoSearchRequest {
  string platform = 1; // 请求来源平台，KwaipilotAgent
  repeated int64 knowledge_repo_ids = 2;
  string query = 3;
  int32 top_k = 4;
  repeated ChatMessage chat_history = 5;
  double threshold = 6; // 相似度阈值
  string cluster = 7;
}

message ChatMessage {
  string role = 1; // user or assistant
  string content = 2;
}

message Knowledge {
  string content = 1;
  string link = 2;
  string title = 3;
  string knowledge_repo_id = 4;
  string knowledge_repo_name = 5;
  float score = 6;
  string tag = 7; // 知识标签
}

message KnowledgeRepoSearchResponse {
  repeated Knowledge knowledge = 1;
  string prompt = 2;
}

message ChatFile {
  string code = 1; // 代码内容
  string name = 2; // 文件名
  string language = 3; // 编程语言
}

message CodeSearchData {
  int64 id = 1;
  string path = 2;
  int64 start_line_no = 3;
  int64 end_line_no = 4;
  int64 start_col_no = 5;
  int64 end_col_no = 6;
  string code = 7;
  string language = 8;
  string function_name = 9;
  string function_signature = 10;
  string code_type = 11;
  string repo_name = 12;
}

message CodeSearchRequest {
  string repo_name = 1; // 库名/仓库名
  string commit_id = 2; // 8位
  repeated string target_directory = 3; // 全仓库搜穿空[]，有指定路径的 llm-server/
  string query = 4;
  repeated ChatFile files = 5;
  string username = 6;
  repeated string repo_names = 7;
  repeated string commit_ids = 8;
  int32 top_k = 9; // 返回结果数量
  double threshold = 10; // 相似度阈值
}

message CodeSearchResponse {
  repeated CodeSearchData list = 1;
  string prompt = 2;
  int32 total = 3;
  int32 status = 4;
}

service RagServerService {
  rpc KnowledgeRepoSearch(KnowledgeRepoSearchRequest) returns (KnowledgeRepoSearchResponse); //知识库检索
  rpc KnowledgeRepoSearchForDataAsset(KnowledgeRepoSearchRequest) returns (KnowledgeRepoSearchResponse); //知识库检索 - for 核心资产
  rpc KnowledgeRepoSearchForRecruit(KnowledgeRepoSearchRequest) returns (KnowledgeRepoSearchResponse); //知识库检索 - for 房聘
  rpc KnowledgeRepoSearchForKwaiBI(KnowledgeRepoSearchRequest) returns (KnowledgeRepoSearchResponse); //知识库检索 - for KwaiBI
  rpc KnowledgeRepoSearchForCluster(KnowledgeRepoSearchRequest) returns (KnowledgeRepoSearchResponse); //知识库检索 - for 独立集群
  rpc CodeSearch (CodeSearchRequest) returns (CodeSearchResponse); //代码搜索
}
