// package: kwaipilot_rag
// file: llm_rag_server.proto

/* tslint:disable */
/* eslint-disable */

import * as grpc from '@infra-node/grpc-js';
import * as llm_rag_server_pb from './llm_rag_server_pb';
import type { Metadata, RPCCallOptions } from '@infra-node/rpc';

export interface IRagServerServiceClient {
  knowledgeRepoSearch(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearch(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearch(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    options: RPC<PERSON>allOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearchForDataAsset(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearchForDataAsset(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearchForDataAsset(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearchForRecruit(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearchForRecruit(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearchForRecruit(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearchForKwaiBI(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearchForKwaiBI(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearchForKwaiBI(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearchForCluster(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearchForCluster(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearchForCluster(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  codeSearch(
    request: llm_rag_server_pb.CodeSearchRequest,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.CodeSearchResponse
    ) => void
  ): void;
  codeSearch(
    request: llm_rag_server_pb.CodeSearchRequest,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.CodeSearchResponse
    ) => void
  ): void;
  codeSearch(
    request: llm_rag_server_pb.CodeSearchRequest,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.CodeSearchResponse
    ) => void
  ): void;
}

export interface IRagServerServiceClientPromisify {
  knowledgeRepoSearch(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearch(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearch(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearchForDataAsset(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearchForDataAsset(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearchForDataAsset(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearchForRecruit(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearchForRecruit(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearchForRecruit(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearchForKwaiBI(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearchForKwaiBI(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearchForKwaiBI(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearchForCluster(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearchForCluster(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearchForCluster(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse>;
  codeSearch(
    request: llm_rag_server_pb.CodeSearchRequest
  ): Promise<llm_rag_server_pb.CodeSearchResponse>;
  codeSearch(
    request: llm_rag_server_pb.CodeSearchRequest,
    metadata: Metadata
  ): Promise<llm_rag_server_pb.CodeSearchResponse>;
  codeSearch(
    request: llm_rag_server_pb.CodeSearchRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_rag_server_pb.CodeSearchResponse>;
}

export interface IRagServerServiceClientDynamic {
  knowledgeRepoSearch(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearch(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearch(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearchForDataAsset(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearchForDataAsset(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearchForDataAsset(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearchForRecruit(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearchForRecruit(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearchForRecruit(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearchForKwaiBI(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearchForKwaiBI(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearchForKwaiBI(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearchForCluster(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearchForCluster(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearchForCluster(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  codeSearch(
    request: llm_rag_server_pb.CodeSearchRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.CodeSearchResponse.DynamicObject
    ) => void
  ): void;
  codeSearch(
    request: llm_rag_server_pb.CodeSearchRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.CodeSearchResponse.DynamicObject
    ) => void
  ): void;
  codeSearch(
    request: llm_rag_server_pb.CodeSearchRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_rag_server_pb.CodeSearchResponse.DynamicObject
    ) => void
  ): void;
}

export interface IRagServerServiceClientDynamicPromisify {
  knowledgeRepoSearch(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearch(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearch(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearchForDataAsset(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearchForDataAsset(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearchForDataAsset(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearchForRecruit(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearchForRecruit(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearchForRecruit(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearchForKwaiBI(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearchForKwaiBI(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearchForKwaiBI(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearchForCluster(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearchForCluster(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearchForCluster(
    request: llm_rag_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_rag_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  codeSearch(
    request: llm_rag_server_pb.CodeSearchRequest.DynamicObject
  ): Promise<llm_rag_server_pb.CodeSearchResponse.DynamicObject>;
  codeSearch(
    request: llm_rag_server_pb.CodeSearchRequest.DynamicObject,
    metadata: Metadata
  ): Promise<llm_rag_server_pb.CodeSearchResponse.DynamicObject>;
  codeSearch(
    request: llm_rag_server_pb.CodeSearchRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_rag_server_pb.CodeSearchResponse.DynamicObject>;
}

export interface IConsumer {
  RagServerService: IRagServerServiceClient;
  __promise__: {
    RagServerService: IRagServerServiceClientPromisify;
  };
}

export interface IConsumerDynamic {
  RagServerService: IRagServerServiceClientDynamic;
  __promise__: {
    RagServerService: IRagServerServiceClientDynamicPromisify;
  };
}
