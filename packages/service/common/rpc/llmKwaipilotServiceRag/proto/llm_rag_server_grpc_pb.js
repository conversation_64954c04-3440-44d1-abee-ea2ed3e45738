// GENERATED CODE -- DO NOT EDIT!

'use strict';
var grpc = require('@infra-node/grpc-js');
var llm_rag_server_pb = require('./llm_rag_server_pb.js');

function serialize_kwaipilot_rag_CodeSearchRequest(arg) {
  if (!(arg instanceof llm_rag_server_pb.CodeSearchRequest)) {
    throw new Error('Expected argument of type kwaipilot_rag.CodeSearchRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_rag_CodeSearchRequest(buffer_arg) {
  return llm_rag_server_pb.CodeSearchRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_rag_CodeSearchResponse(arg) {
  if (!(arg instanceof llm_rag_server_pb.CodeSearchResponse)) {
    throw new Error('Expected argument of type kwaipilot_rag.CodeSearchResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_rag_CodeSearchResponse(buffer_arg) {
  return llm_rag_server_pb.CodeSearchResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_rag_KnowledgeRepoSearchRequest(arg) {
  if (!(arg instanceof llm_rag_server_pb.KnowledgeRepoSearchRequest)) {
    throw new Error('Expected argument of type kwaipilot_rag.KnowledgeRepoSearchRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_rag_KnowledgeRepoSearchRequest(buffer_arg) {
  return llm_rag_server_pb.KnowledgeRepoSearchRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_rag_KnowledgeRepoSearchResponse(arg) {
  if (!(arg instanceof llm_rag_server_pb.KnowledgeRepoSearchResponse)) {
    throw new Error('Expected argument of type kwaipilot_rag.KnowledgeRepoSearchResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_rag_KnowledgeRepoSearchResponse(buffer_arg) {
  return llm_rag_server_pb.KnowledgeRepoSearchResponse.deserializeBinary(
    new Uint8Array(buffer_arg)
  );
}

var RagServerServiceService = (exports.RagServerServiceService = {
  knowledgeRepoSearch: {
    path: '/kwaipilot_rag.RagServerService/KnowledgeRepoSearch',
    requestStream: false,
    responseStream: false,
    requestType: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    responseType: llm_rag_server_pb.KnowledgeRepoSearchResponse,
    requestSerialize: serialize_kwaipilot_rag_KnowledgeRepoSearchRequest,
    requestDeserialize: deserialize_kwaipilot_rag_KnowledgeRepoSearchRequest,
    responseSerialize: serialize_kwaipilot_rag_KnowledgeRepoSearchResponse,
    responseDeserialize: deserialize_kwaipilot_rag_KnowledgeRepoSearchResponse
  },
  // 知识库检索
  knowledgeRepoSearchForDataAsset: {
    path: '/kwaipilot_rag.RagServerService/KnowledgeRepoSearchForDataAsset',
    requestStream: false,
    responseStream: false,
    requestType: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    responseType: llm_rag_server_pb.KnowledgeRepoSearchResponse,
    requestSerialize: serialize_kwaipilot_rag_KnowledgeRepoSearchRequest,
    requestDeserialize: deserialize_kwaipilot_rag_KnowledgeRepoSearchRequest,
    responseSerialize: serialize_kwaipilot_rag_KnowledgeRepoSearchResponse,
    responseDeserialize: deserialize_kwaipilot_rag_KnowledgeRepoSearchResponse
  },
  // 知识库检索 - for 核心资产
  knowledgeRepoSearchForRecruit: {
    path: '/kwaipilot_rag.RagServerService/KnowledgeRepoSearchForRecruit',
    requestStream: false,
    responseStream: false,
    requestType: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    responseType: llm_rag_server_pb.KnowledgeRepoSearchResponse,
    requestSerialize: serialize_kwaipilot_rag_KnowledgeRepoSearchRequest,
    requestDeserialize: deserialize_kwaipilot_rag_KnowledgeRepoSearchRequest,
    responseSerialize: serialize_kwaipilot_rag_KnowledgeRepoSearchResponse,
    responseDeserialize: deserialize_kwaipilot_rag_KnowledgeRepoSearchResponse
  },
  // 知识库检索 - for 房聘
  knowledgeRepoSearchForKwaiBI: {
    path: '/kwaipilot_rag.RagServerService/KnowledgeRepoSearchForKwaiBI',
    requestStream: false,
    responseStream: false,
    requestType: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    responseType: llm_rag_server_pb.KnowledgeRepoSearchResponse,
    requestSerialize: serialize_kwaipilot_rag_KnowledgeRepoSearchRequest,
    requestDeserialize: deserialize_kwaipilot_rag_KnowledgeRepoSearchRequest,
    responseSerialize: serialize_kwaipilot_rag_KnowledgeRepoSearchResponse,
    responseDeserialize: deserialize_kwaipilot_rag_KnowledgeRepoSearchResponse
  },
  // 知识库检索 - for KwaiBI
  knowledgeRepoSearchForCluster: {
    path: '/kwaipilot_rag.RagServerService/KnowledgeRepoSearchForCluster',
    requestStream: false,
    responseStream: false,
    requestType: llm_rag_server_pb.KnowledgeRepoSearchRequest,
    responseType: llm_rag_server_pb.KnowledgeRepoSearchResponse,
    requestSerialize: serialize_kwaipilot_rag_KnowledgeRepoSearchRequest,
    requestDeserialize: deserialize_kwaipilot_rag_KnowledgeRepoSearchRequest,
    responseSerialize: serialize_kwaipilot_rag_KnowledgeRepoSearchResponse,
    responseDeserialize: deserialize_kwaipilot_rag_KnowledgeRepoSearchResponse
  },
  // 知识库检索 - for 独立集群
  codeSearch: {
    path: '/kwaipilot_rag.RagServerService/CodeSearch',
    requestStream: false,
    responseStream: false,
    requestType: llm_rag_server_pb.CodeSearchRequest,
    responseType: llm_rag_server_pb.CodeSearchResponse,
    requestSerialize: serialize_kwaipilot_rag_CodeSearchRequest,
    requestDeserialize: deserialize_kwaipilot_rag_CodeSearchRequest,
    responseSerialize: serialize_kwaipilot_rag_CodeSearchResponse,
    responseDeserialize: deserialize_kwaipilot_rag_CodeSearchResponse
  }
  // 代码搜索
});

exports.RagServerServiceClient = grpc.makeGenericClientConstructor(RagServerServiceService);
