// package: kwaipilot_rag
// file: llm_rag_server.proto

/* tslint:disable */
/* eslint-disable */

import * as jspb from 'google-protobuf';

export class KnowledgeRepoSearchRequest extends jspb.Message {
  getPlatform(): string;
  setPlatform(value: string): KnowledgeRepoSearchRequest;
  clearKnowledgeRepoIdsList(): void;
  getKnowledgeRepoIdsList(): Array<number>;
  setKnowledgeRepoIdsList(value: Array<number>): KnowledgeRepoSearchRequest;
  addKnowledgeRepoIds(value: number, index?: number): number;
  getQuery(): string;
  setQuery(value: string): KnowledgeRepoSearchRequest;
  getTopK(): number;
  setTopK(value: number): KnowledgeRepoSearchRequest;
  clearChatHistoryList(): void;
  getChatHistoryList(): Array<ChatMessage>;
  setChatHistoryList(value: Array<ChatMessage>): KnowledgeRepoSearchRequest;
  addChatHistory(value?: ChatMessage, index?: number): ChatMessage;
  getThreshold(): number;
  setThreshold(value: number): KnowledgeRepoSearchRequest;
  getCluster(): string;
  setCluster(value: string): KnowledgeRepoSearchRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KnowledgeRepoSearchRequest.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KnowledgeRepoSearchRequest
  ): KnowledgeRepoSearchRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KnowledgeRepoSearchRequest,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KnowledgeRepoSearchRequest;
  static deserializeBinaryFromReader(
    message: KnowledgeRepoSearchRequest,
    reader: jspb.BinaryReader
  ): KnowledgeRepoSearchRequest;
}

export namespace KnowledgeRepoSearchRequest {
  export type AsObject = {
    platform: string;
    knowledgeRepoIdsList: Array<number>;
    query: string;
    topK: number;
    chatHistoryList: Array<ChatMessage.AsObject>;
    threshold: number;
    cluster: string;
  };

  export type DynamicObject = {
    platform: string;
    knowledgeRepoIds: Array<string>;
    query: string;
    topK: number;
    chatHistory: Array<ChatMessage.DynamicObject>;
    threshold: number;
    cluster: string;
  };
}

export class ChatMessage extends jspb.Message {
  getRole(): string;
  setRole(value: string): ChatMessage;
  getContent(): string;
  setContent(value: string): ChatMessage;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ChatMessage.AsObject;
  static toObject(includeInstance: boolean, msg: ChatMessage): ChatMessage.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: ChatMessage, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ChatMessage;
  static deserializeBinaryFromReader(message: ChatMessage, reader: jspb.BinaryReader): ChatMessage;
}

export namespace ChatMessage {
  export type AsObject = {
    role: string;
    content: string;
  };

  export type DynamicObject = {
    role: string;
    content: string;
  };
}

export class Knowledge extends jspb.Message {
  getContent(): string;
  setContent(value: string): Knowledge;
  getLink(): string;
  setLink(value: string): Knowledge;
  getTitle(): string;
  setTitle(value: string): Knowledge;
  getKnowledgeRepoId(): string;
  setKnowledgeRepoId(value: string): Knowledge;
  getKnowledgeRepoName(): string;
  setKnowledgeRepoName(value: string): Knowledge;
  getScore(): number;
  setScore(value: number): Knowledge;
  getTag(): string;
  setTag(value: string): Knowledge;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Knowledge.AsObject;
  static toObject(includeInstance: boolean, msg: Knowledge): Knowledge.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: Knowledge, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Knowledge;
  static deserializeBinaryFromReader(message: Knowledge, reader: jspb.BinaryReader): Knowledge;
}

export namespace Knowledge {
  export type AsObject = {
    content: string;
    link: string;
    title: string;
    knowledgeRepoId: string;
    knowledgeRepoName: string;
    score: number;
    tag: string;
  };

  export type DynamicObject = {
    content: string;
    link: string;
    title: string;
    knowledgeRepoId: string;
    knowledgeRepoName: string;
    score: number;
    tag: string;
  };
}

export class KnowledgeRepoSearchResponse extends jspb.Message {
  clearKnowledgeList(): void;
  getKnowledgeList(): Array<Knowledge>;
  setKnowledgeList(value: Array<Knowledge>): KnowledgeRepoSearchResponse;
  addKnowledge(value?: Knowledge, index?: number): Knowledge;
  getPrompt(): string;
  setPrompt(value: string): KnowledgeRepoSearchResponse;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KnowledgeRepoSearchResponse.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KnowledgeRepoSearchResponse
  ): KnowledgeRepoSearchResponse.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KnowledgeRepoSearchResponse,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KnowledgeRepoSearchResponse;
  static deserializeBinaryFromReader(
    message: KnowledgeRepoSearchResponse,
    reader: jspb.BinaryReader
  ): KnowledgeRepoSearchResponse;
}

export namespace KnowledgeRepoSearchResponse {
  export type AsObject = {
    knowledgeList: Array<Knowledge.AsObject>;
    prompt: string;
  };

  export type DynamicObject = {
    knowledge: Array<Knowledge.DynamicObject>;
    prompt: string;
  };
}

export class ChatFile extends jspb.Message {
  getCode(): string;
  setCode(value: string): ChatFile;
  getName(): string;
  setName(value: string): ChatFile;
  getLanguage(): string;
  setLanguage(value: string): ChatFile;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ChatFile.AsObject;
  static toObject(includeInstance: boolean, msg: ChatFile): ChatFile.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: ChatFile, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ChatFile;
  static deserializeBinaryFromReader(message: ChatFile, reader: jspb.BinaryReader): ChatFile;
}

export namespace ChatFile {
  export type AsObject = {
    code: string;
    name: string;
    language: string;
  };

  export type DynamicObject = {
    code: string;
    name: string;
    language: string;
  };
}

export class CodeSearchData extends jspb.Message {
  getId(): number;
  setId(value: number): CodeSearchData;
  getPath(): string;
  setPath(value: string): CodeSearchData;
  getStartLineNo(): number;
  setStartLineNo(value: number): CodeSearchData;
  getEndLineNo(): number;
  setEndLineNo(value: number): CodeSearchData;
  getStartColNo(): number;
  setStartColNo(value: number): CodeSearchData;
  getEndColNo(): number;
  setEndColNo(value: number): CodeSearchData;
  getCode(): string;
  setCode(value: string): CodeSearchData;
  getLanguage(): string;
  setLanguage(value: string): CodeSearchData;
  getFunctionName(): string;
  setFunctionName(value: string): CodeSearchData;
  getFunctionSignature(): string;
  setFunctionSignature(value: string): CodeSearchData;
  getCodeType(): string;
  setCodeType(value: string): CodeSearchData;
  getRepoName(): string;
  setRepoName(value: string): CodeSearchData;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CodeSearchData.AsObject;
  static toObject(includeInstance: boolean, msg: CodeSearchData): CodeSearchData.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: CodeSearchData, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CodeSearchData;
  static deserializeBinaryFromReader(
    message: CodeSearchData,
    reader: jspb.BinaryReader
  ): CodeSearchData;
}

export namespace CodeSearchData {
  export type AsObject = {
    id: number;
    path: string;
    startLineNo: number;
    endLineNo: number;
    startColNo: number;
    endColNo: number;
    code: string;
    language: string;
    functionName: string;
    functionSignature: string;
    codeType: string;
    repoName: string;
  };

  export type DynamicObject = {
    id: string;
    path: string;
    startLineNo: string;
    endLineNo: string;
    startColNo: string;
    endColNo: string;
    code: string;
    language: string;
    functionName: string;
    functionSignature: string;
    codeType: string;
    repoName: string;
  };
}

export class CodeSearchRequest extends jspb.Message {
  getRepoName(): string;
  setRepoName(value: string): CodeSearchRequest;
  getCommitId(): string;
  setCommitId(value: string): CodeSearchRequest;
  clearTargetDirectoryList(): void;
  getTargetDirectoryList(): Array<string>;
  setTargetDirectoryList(value: Array<string>): CodeSearchRequest;
  addTargetDirectory(value: string, index?: number): string;
  getQuery(): string;
  setQuery(value: string): CodeSearchRequest;
  clearFilesList(): void;
  getFilesList(): Array<ChatFile>;
  setFilesList(value: Array<ChatFile>): CodeSearchRequest;
  addFiles(value?: ChatFile, index?: number): ChatFile;
  getUsername(): string;
  setUsername(value: string): CodeSearchRequest;
  clearRepoNamesList(): void;
  getRepoNamesList(): Array<string>;
  setRepoNamesList(value: Array<string>): CodeSearchRequest;
  addRepoNames(value: string, index?: number): string;
  clearCommitIdsList(): void;
  getCommitIdsList(): Array<string>;
  setCommitIdsList(value: Array<string>): CodeSearchRequest;
  addCommitIds(value: string, index?: number): string;
  getTopK(): number;
  setTopK(value: number): CodeSearchRequest;
  getThreshold(): number;
  setThreshold(value: number): CodeSearchRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CodeSearchRequest.AsObject;
  static toObject(includeInstance: boolean, msg: CodeSearchRequest): CodeSearchRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: CodeSearchRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CodeSearchRequest;
  static deserializeBinaryFromReader(
    message: CodeSearchRequest,
    reader: jspb.BinaryReader
  ): CodeSearchRequest;
}

export namespace CodeSearchRequest {
  export type AsObject = {
    repoName: string;
    commitId: string;
    targetDirectoryList: Array<string>;
    query: string;
    filesList: Array<ChatFile.AsObject>;
    username: string;
    repoNamesList: Array<string>;
    commitIdsList: Array<string>;
    topK: number;
    threshold: number;
  };

  export type DynamicObject = {
    repoName: string;
    commitId: string;
    targetDirectory: Array<string>;
    query: string;
    files: Array<ChatFile.DynamicObject>;
    username: string;
    repoNames: Array<string>;
    commitIds: Array<string>;
    topK: number;
    threshold: number;
  };
}

export class CodeSearchResponse extends jspb.Message {
  clearListList(): void;
  getListList(): Array<CodeSearchData>;
  setListList(value: Array<CodeSearchData>): CodeSearchResponse;
  addList(value?: CodeSearchData, index?: number): CodeSearchData;
  getPrompt(): string;
  setPrompt(value: string): CodeSearchResponse;
  getTotal(): number;
  setTotal(value: number): CodeSearchResponse;
  getStatus(): number;
  setStatus(value: number): CodeSearchResponse;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CodeSearchResponse.AsObject;
  static toObject(includeInstance: boolean, msg: CodeSearchResponse): CodeSearchResponse.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: CodeSearchResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CodeSearchResponse;
  static deserializeBinaryFromReader(
    message: CodeSearchResponse,
    reader: jspb.BinaryReader
  ): CodeSearchResponse;
}

export namespace CodeSearchResponse {
  export type AsObject = {
    listList: Array<CodeSearchData.AsObject>;
    prompt: string;
    total: number;
    status: number;
  };

  export type DynamicObject = {
    list: Array<CodeSearchData.DynamicObject>;
    prompt: string;
    total: number;
    status: number;
  };
}
