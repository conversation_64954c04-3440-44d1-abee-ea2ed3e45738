// source: llm_rag_server.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.kwaipilot_rag.ChatFile', null, global);
goog.exportSymbol('proto.kwaipilot_rag.ChatMessage', null, global);
goog.exportSymbol('proto.kwaipilot_rag.CodeSearchData', null, global);
goog.exportSymbol('proto.kwaipilot_rag.CodeSearchRequest', null, global);
goog.exportSymbol('proto.kwaipilot_rag.CodeSearchResponse', null, global);
goog.exportSymbol('proto.kwaipilot_rag.Knowledge', null, global);
goog.exportSymbol('proto.kwaipilot_rag.KnowledgeRepoSearchRequest', null, global);
goog.exportSymbol('proto.kwaipilot_rag.KnowledgeRepoSearchResponse', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest = function (opt_data) {
  jspb.Message.initialize(
    this,
    opt_data,
    0,
    -1,
    proto.kwaipilot_rag.KnowledgeRepoSearchRequest.repeatedFields_,
    null
  );
};
goog.inherits(proto.kwaipilot_rag.KnowledgeRepoSearchRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_rag.KnowledgeRepoSearchRequest.displayName =
    'proto.kwaipilot_rag.KnowledgeRepoSearchRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_rag.ChatMessage = function (opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot_rag.ChatMessage, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_rag.ChatMessage.displayName = 'proto.kwaipilot_rag.ChatMessage';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_rag.Knowledge = function (opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot_rag.Knowledge, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_rag.Knowledge.displayName = 'proto.kwaipilot_rag.Knowledge';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_rag.KnowledgeRepoSearchResponse = function (opt_data) {
  jspb.Message.initialize(
    this,
    opt_data,
    0,
    -1,
    proto.kwaipilot_rag.KnowledgeRepoSearchResponse.repeatedFields_,
    null
  );
};
goog.inherits(proto.kwaipilot_rag.KnowledgeRepoSearchResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_rag.KnowledgeRepoSearchResponse.displayName =
    'proto.kwaipilot_rag.KnowledgeRepoSearchResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_rag.ChatFile = function (opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot_rag.ChatFile, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_rag.ChatFile.displayName = 'proto.kwaipilot_rag.ChatFile';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_rag.CodeSearchData = function (opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot_rag.CodeSearchData, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_rag.CodeSearchData.displayName = 'proto.kwaipilot_rag.CodeSearchData';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_rag.CodeSearchRequest = function (opt_data) {
  jspb.Message.initialize(
    this,
    opt_data,
    0,
    -1,
    proto.kwaipilot_rag.CodeSearchRequest.repeatedFields_,
    null
  );
};
goog.inherits(proto.kwaipilot_rag.CodeSearchRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_rag.CodeSearchRequest.displayName = 'proto.kwaipilot_rag.CodeSearchRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_rag.CodeSearchResponse = function (opt_data) {
  jspb.Message.initialize(
    this,
    opt_data,
    0,
    -1,
    proto.kwaipilot_rag.CodeSearchResponse.repeatedFields_,
    null
  );
};
goog.inherits(proto.kwaipilot_rag.CodeSearchResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_rag.CodeSearchResponse.displayName = 'proto.kwaipilot_rag.CodeSearchResponse';
}

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.repeatedFields_ = [2, 5];

if (jspb.Message.GENERATE_TO_OBJECT) {
  /**
   * Creates an object representation of this proto.
   * Field names that are reserved in JavaScript and will be renamed to pb_name.
   * Optional fields that are not set will be set to undefined.
   * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
   * For the list of reserved names please see:
   *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
   * @param {boolean=} opt_includeInstance Deprecated. whether to include the
   *     JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @return {!Object}
   */
  proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.toObject = function (
    opt_includeInstance
  ) {
    return proto.kwaipilot_rag.KnowledgeRepoSearchRequest.toObject(opt_includeInstance, this);
  };

  /**
   * Static version of the {@see toObject} method.
   * @param {boolean|undefined} includeInstance Deprecated. Whether to include
   *     the JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @param {!proto.kwaipilot_rag.KnowledgeRepoSearchRequest} msg The msg instance to transform.
   * @return {!Object}
   * @suppress {unusedLocalVariables} f is only used for nested messages
   */
  proto.kwaipilot_rag.KnowledgeRepoSearchRequest.toObject = function (includeInstance, msg) {
    var f,
      obj = {
        platform: jspb.Message.getFieldWithDefault(msg, 1, ''),
        knowledgeRepoIdsList: (f = jspb.Message.getRepeatedField(msg, 2)) == null ? undefined : f,
        query: jspb.Message.getFieldWithDefault(msg, 3, ''),
        topK: jspb.Message.getFieldWithDefault(msg, 4, 0),
        chatHistoryList: jspb.Message.toObjectList(
          msg.getChatHistoryList(),
          proto.kwaipilot_rag.ChatMessage.toObject,
          includeInstance
        ),
        threshold: jspb.Message.getFloatingPointFieldWithDefault(msg, 6, 0.0),
        cluster: jspb.Message.getFieldWithDefault(msg, 7, '')
      };

    if (includeInstance) {
      obj.$jspbMessageInstance = msg;
    }
    return obj;
  };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_rag.KnowledgeRepoSearchRequest}
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.deserializeBinary = function (bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_rag.KnowledgeRepoSearchRequest();
  return proto.kwaipilot_rag.KnowledgeRepoSearchRequest.deserializeBinaryFromReader(msg, reader);
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_rag.KnowledgeRepoSearchRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_rag.KnowledgeRepoSearchRequest}
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.deserializeBinaryFromReader = function (
  msg,
  reader
) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
      case 1:
        var value = /** @type {string} */ (reader.readString());
        msg.setPlatform(value);
        break;
      case 2:
        var values = /** @type {!Array<number>} */ (
          reader.isDelimited() ? reader.readPackedInt64() : [reader.readInt64()]
        );
        for (var i = 0; i < values.length; i++) {
          msg.addKnowledgeRepoIds(values[i]);
        }
        break;
      case 3:
        var value = /** @type {string} */ (reader.readString());
        msg.setQuery(value);
        break;
      case 4:
        var value = /** @type {number} */ (reader.readInt32());
        msg.setTopK(value);
        break;
      case 5:
        var value = new proto.kwaipilot_rag.ChatMessage();
        reader.readMessage(value, proto.kwaipilot_rag.ChatMessage.deserializeBinaryFromReader);
        msg.addChatHistory(value);
        break;
      case 6:
        var value = /** @type {number} */ (reader.readDouble());
        msg.setThreshold(value);
        break;
      case 7:
        var value = /** @type {string} */ (reader.readString());
        msg.setCluster(value);
        break;
      default:
        reader.skipField();
        break;
    }
  }
  return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.serializeBinary = function () {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_rag.KnowledgeRepoSearchRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_rag.KnowledgeRepoSearchRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.serializeBinaryToWriter = function (
  message,
  writer
) {
  var f = undefined;
  f = message.getPlatform();
  if (f.length > 0) {
    writer.writeString(1, f);
  }
  f = message.getKnowledgeRepoIdsList();
  if (f.length > 0) {
    writer.writePackedInt64(2, f);
  }
  f = message.getQuery();
  if (f.length > 0) {
    writer.writeString(3, f);
  }
  f = message.getTopK();
  if (f !== 0) {
    writer.writeInt32(4, f);
  }
  f = message.getChatHistoryList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(5, f, proto.kwaipilot_rag.ChatMessage.serializeBinaryToWriter);
  }
  f = message.getThreshold();
  if (f !== 0.0) {
    writer.writeDouble(6, f);
  }
  f = message.getCluster();
  if (f.length > 0) {
    writer.writeString(7, f);
  }
};

/**
 * optional string platform = 1;
 * @return {string}
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.getPlatform = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.setPlatform = function (value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};

/**
 * repeated int64 knowledge_repo_ids = 2;
 * @return {!Array<number>}
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.getKnowledgeRepoIdsList = function () {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedField(this, 2));
};

/**
 * @param {!Array<number>} value
 * @return {!proto.kwaipilot_rag.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.setKnowledgeRepoIdsList = function (
  value
) {
  return jspb.Message.setField(this, 2, value || []);
};

/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_rag.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.addKnowledgeRepoIds = function (
  value,
  opt_index
) {
  return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};

/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_rag.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.clearKnowledgeRepoIdsList = function () {
  return this.setKnowledgeRepoIdsList([]);
};

/**
 * optional string query = 3;
 * @return {string}
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.getQuery = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.setQuery = function (value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};

/**
 * optional int32 top_k = 4;
 * @return {number}
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.getTopK = function () {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};

/**
 * @param {number} value
 * @return {!proto.kwaipilot_rag.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.setTopK = function (value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};

/**
 * repeated ChatMessage chat_history = 5;
 * @return {!Array<!proto.kwaipilot_rag.ChatMessage>}
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.getChatHistoryList = function () {
  return /** @type{!Array<!proto.kwaipilot_rag.ChatMessage>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot_rag.ChatMessage, 5)
  );
};

/**
 * @param {!Array<!proto.kwaipilot_rag.ChatMessage>} value
 * @return {!proto.kwaipilot_rag.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.setChatHistoryList = function (value) {
  return jspb.Message.setRepeatedWrapperField(this, 5, value);
};

/**
 * @param {!proto.kwaipilot_rag.ChatMessage=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_rag.ChatMessage}
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.addChatHistory = function (
  opt_value,
  opt_index
) {
  return jspb.Message.addToRepeatedWrapperField(
    this,
    5,
    opt_value,
    proto.kwaipilot_rag.ChatMessage,
    opt_index
  );
};

/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_rag.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.clearChatHistoryList = function () {
  return this.setChatHistoryList([]);
};

/**
 * optional double threshold = 6;
 * @return {number}
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.getThreshold = function () {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 6, 0.0));
};

/**
 * @param {number} value
 * @return {!proto.kwaipilot_rag.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.setThreshold = function (value) {
  return jspb.Message.setProto3FloatField(this, 6, value);
};

/**
 * optional string cluster = 7;
 * @return {string}
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.getCluster = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot_rag.KnowledgeRepoSearchRequest.prototype.setCluster = function (value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};

if (jspb.Message.GENERATE_TO_OBJECT) {
  /**
   * Creates an object representation of this proto.
   * Field names that are reserved in JavaScript and will be renamed to pb_name.
   * Optional fields that are not set will be set to undefined.
   * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
   * For the list of reserved names please see:
   *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
   * @param {boolean=} opt_includeInstance Deprecated. whether to include the
   *     JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @return {!Object}
   */
  proto.kwaipilot_rag.ChatMessage.prototype.toObject = function (opt_includeInstance) {
    return proto.kwaipilot_rag.ChatMessage.toObject(opt_includeInstance, this);
  };

  /**
   * Static version of the {@see toObject} method.
   * @param {boolean|undefined} includeInstance Deprecated. Whether to include
   *     the JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @param {!proto.kwaipilot_rag.ChatMessage} msg The msg instance to transform.
   * @return {!Object}
   * @suppress {unusedLocalVariables} f is only used for nested messages
   */
  proto.kwaipilot_rag.ChatMessage.toObject = function (includeInstance, msg) {
    var f,
      obj = {
        role: jspb.Message.getFieldWithDefault(msg, 1, ''),
        content: jspb.Message.getFieldWithDefault(msg, 2, '')
      };

    if (includeInstance) {
      obj.$jspbMessageInstance = msg;
    }
    return obj;
  };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_rag.ChatMessage}
 */
proto.kwaipilot_rag.ChatMessage.deserializeBinary = function (bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_rag.ChatMessage();
  return proto.kwaipilot_rag.ChatMessage.deserializeBinaryFromReader(msg, reader);
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_rag.ChatMessage} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_rag.ChatMessage}
 */
proto.kwaipilot_rag.ChatMessage.deserializeBinaryFromReader = function (msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
      case 1:
        var value = /** @type {string} */ (reader.readString());
        msg.setRole(value);
        break;
      case 2:
        var value = /** @type {string} */ (reader.readString());
        msg.setContent(value);
        break;
      default:
        reader.skipField();
        break;
    }
  }
  return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_rag.ChatMessage.prototype.serializeBinary = function () {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_rag.ChatMessage.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_rag.ChatMessage} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_rag.ChatMessage.serializeBinaryToWriter = function (message, writer) {
  var f = undefined;
  f = message.getRole();
  if (f.length > 0) {
    writer.writeString(1, f);
  }
  f = message.getContent();
  if (f.length > 0) {
    writer.writeString(2, f);
  }
};

/**
 * optional string role = 1;
 * @return {string}
 */
proto.kwaipilot_rag.ChatMessage.prototype.getRole = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.ChatMessage} returns this
 */
proto.kwaipilot_rag.ChatMessage.prototype.setRole = function (value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};

/**
 * optional string content = 2;
 * @return {string}
 */
proto.kwaipilot_rag.ChatMessage.prototype.getContent = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.ChatMessage} returns this
 */
proto.kwaipilot_rag.ChatMessage.prototype.setContent = function (value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};

if (jspb.Message.GENERATE_TO_OBJECT) {
  /**
   * Creates an object representation of this proto.
   * Field names that are reserved in JavaScript and will be renamed to pb_name.
   * Optional fields that are not set will be set to undefined.
   * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
   * For the list of reserved names please see:
   *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
   * @param {boolean=} opt_includeInstance Deprecated. whether to include the
   *     JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @return {!Object}
   */
  proto.kwaipilot_rag.Knowledge.prototype.toObject = function (opt_includeInstance) {
    return proto.kwaipilot_rag.Knowledge.toObject(opt_includeInstance, this);
  };

  /**
   * Static version of the {@see toObject} method.
   * @param {boolean|undefined} includeInstance Deprecated. Whether to include
   *     the JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @param {!proto.kwaipilot_rag.Knowledge} msg The msg instance to transform.
   * @return {!Object}
   * @suppress {unusedLocalVariables} f is only used for nested messages
   */
  proto.kwaipilot_rag.Knowledge.toObject = function (includeInstance, msg) {
    var f,
      obj = {
        content: jspb.Message.getFieldWithDefault(msg, 1, ''),
        link: jspb.Message.getFieldWithDefault(msg, 2, ''),
        title: jspb.Message.getFieldWithDefault(msg, 3, ''),
        knowledgeRepoId: jspb.Message.getFieldWithDefault(msg, 4, ''),
        knowledgeRepoName: jspb.Message.getFieldWithDefault(msg, 5, ''),
        score: jspb.Message.getFloatingPointFieldWithDefault(msg, 6, 0.0),
        tag: jspb.Message.getFieldWithDefault(msg, 7, '')
      };

    if (includeInstance) {
      obj.$jspbMessageInstance = msg;
    }
    return obj;
  };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_rag.Knowledge}
 */
proto.kwaipilot_rag.Knowledge.deserializeBinary = function (bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_rag.Knowledge();
  return proto.kwaipilot_rag.Knowledge.deserializeBinaryFromReader(msg, reader);
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_rag.Knowledge} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_rag.Knowledge}
 */
proto.kwaipilot_rag.Knowledge.deserializeBinaryFromReader = function (msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
      case 1:
        var value = /** @type {string} */ (reader.readString());
        msg.setContent(value);
        break;
      case 2:
        var value = /** @type {string} */ (reader.readString());
        msg.setLink(value);
        break;
      case 3:
        var value = /** @type {string} */ (reader.readString());
        msg.setTitle(value);
        break;
      case 4:
        var value = /** @type {string} */ (reader.readString());
        msg.setKnowledgeRepoId(value);
        break;
      case 5:
        var value = /** @type {string} */ (reader.readString());
        msg.setKnowledgeRepoName(value);
        break;
      case 6:
        var value = /** @type {number} */ (reader.readFloat());
        msg.setScore(value);
        break;
      case 7:
        var value = /** @type {string} */ (reader.readString());
        msg.setTag(value);
        break;
      default:
        reader.skipField();
        break;
    }
  }
  return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_rag.Knowledge.prototype.serializeBinary = function () {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_rag.Knowledge.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_rag.Knowledge} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_rag.Knowledge.serializeBinaryToWriter = function (message, writer) {
  var f = undefined;
  f = message.getContent();
  if (f.length > 0) {
    writer.writeString(1, f);
  }
  f = message.getLink();
  if (f.length > 0) {
    writer.writeString(2, f);
  }
  f = message.getTitle();
  if (f.length > 0) {
    writer.writeString(3, f);
  }
  f = message.getKnowledgeRepoId();
  if (f.length > 0) {
    writer.writeString(4, f);
  }
  f = message.getKnowledgeRepoName();
  if (f.length > 0) {
    writer.writeString(5, f);
  }
  f = message.getScore();
  if (f !== 0.0) {
    writer.writeFloat(6, f);
  }
  f = message.getTag();
  if (f.length > 0) {
    writer.writeString(7, f);
  }
};

/**
 * optional string content = 1;
 * @return {string}
 */
proto.kwaipilot_rag.Knowledge.prototype.getContent = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.Knowledge} returns this
 */
proto.kwaipilot_rag.Knowledge.prototype.setContent = function (value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};

/**
 * optional string link = 2;
 * @return {string}
 */
proto.kwaipilot_rag.Knowledge.prototype.getLink = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.Knowledge} returns this
 */
proto.kwaipilot_rag.Knowledge.prototype.setLink = function (value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};

/**
 * optional string title = 3;
 * @return {string}
 */
proto.kwaipilot_rag.Knowledge.prototype.getTitle = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.Knowledge} returns this
 */
proto.kwaipilot_rag.Knowledge.prototype.setTitle = function (value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};

/**
 * optional string knowledge_repo_id = 4;
 * @return {string}
 */
proto.kwaipilot_rag.Knowledge.prototype.getKnowledgeRepoId = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.Knowledge} returns this
 */
proto.kwaipilot_rag.Knowledge.prototype.setKnowledgeRepoId = function (value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};

/**
 * optional string knowledge_repo_name = 5;
 * @return {string}
 */
proto.kwaipilot_rag.Knowledge.prototype.getKnowledgeRepoName = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.Knowledge} returns this
 */
proto.kwaipilot_rag.Knowledge.prototype.setKnowledgeRepoName = function (value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};

/**
 * optional float score = 6;
 * @return {number}
 */
proto.kwaipilot_rag.Knowledge.prototype.getScore = function () {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 6, 0.0));
};

/**
 * @param {number} value
 * @return {!proto.kwaipilot_rag.Knowledge} returns this
 */
proto.kwaipilot_rag.Knowledge.prototype.setScore = function (value) {
  return jspb.Message.setProto3FloatField(this, 6, value);
};

/**
 * optional string tag = 7;
 * @return {string}
 */
proto.kwaipilot_rag.Knowledge.prototype.getTag = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.Knowledge} returns this
 */
proto.kwaipilot_rag.Knowledge.prototype.setTag = function (value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot_rag.KnowledgeRepoSearchResponse.repeatedFields_ = [1];

if (jspb.Message.GENERATE_TO_OBJECT) {
  /**
   * Creates an object representation of this proto.
   * Field names that are reserved in JavaScript and will be renamed to pb_name.
   * Optional fields that are not set will be set to undefined.
   * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
   * For the list of reserved names please see:
   *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
   * @param {boolean=} opt_includeInstance Deprecated. whether to include the
   *     JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @return {!Object}
   */
  proto.kwaipilot_rag.KnowledgeRepoSearchResponse.prototype.toObject = function (
    opt_includeInstance
  ) {
    return proto.kwaipilot_rag.KnowledgeRepoSearchResponse.toObject(opt_includeInstance, this);
  };

  /**
   * Static version of the {@see toObject} method.
   * @param {boolean|undefined} includeInstance Deprecated. Whether to include
   *     the JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @param {!proto.kwaipilot_rag.KnowledgeRepoSearchResponse} msg The msg instance to transform.
   * @return {!Object}
   * @suppress {unusedLocalVariables} f is only used for nested messages
   */
  proto.kwaipilot_rag.KnowledgeRepoSearchResponse.toObject = function (includeInstance, msg) {
    var f,
      obj = {
        knowledgeList: jspb.Message.toObjectList(
          msg.getKnowledgeList(),
          proto.kwaipilot_rag.Knowledge.toObject,
          includeInstance
        ),
        prompt: jspb.Message.getFieldWithDefault(msg, 2, '')
      };

    if (includeInstance) {
      obj.$jspbMessageInstance = msg;
    }
    return obj;
  };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_rag.KnowledgeRepoSearchResponse}
 */
proto.kwaipilot_rag.KnowledgeRepoSearchResponse.deserializeBinary = function (bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_rag.KnowledgeRepoSearchResponse();
  return proto.kwaipilot_rag.KnowledgeRepoSearchResponse.deserializeBinaryFromReader(msg, reader);
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_rag.KnowledgeRepoSearchResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_rag.KnowledgeRepoSearchResponse}
 */
proto.kwaipilot_rag.KnowledgeRepoSearchResponse.deserializeBinaryFromReader = function (
  msg,
  reader
) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
      case 1:
        var value = new proto.kwaipilot_rag.Knowledge();
        reader.readMessage(value, proto.kwaipilot_rag.Knowledge.deserializeBinaryFromReader);
        msg.addKnowledge(value);
        break;
      case 2:
        var value = /** @type {string} */ (reader.readString());
        msg.setPrompt(value);
        break;
      default:
        reader.skipField();
        break;
    }
  }
  return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_rag.KnowledgeRepoSearchResponse.prototype.serializeBinary = function () {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_rag.KnowledgeRepoSearchResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_rag.KnowledgeRepoSearchResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_rag.KnowledgeRepoSearchResponse.serializeBinaryToWriter = function (
  message,
  writer
) {
  var f = undefined;
  f = message.getKnowledgeList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(1, f, proto.kwaipilot_rag.Knowledge.serializeBinaryToWriter);
  }
  f = message.getPrompt();
  if (f.length > 0) {
    writer.writeString(2, f);
  }
};

/**
 * repeated Knowledge knowledge = 1;
 * @return {!Array<!proto.kwaipilot_rag.Knowledge>}
 */
proto.kwaipilot_rag.KnowledgeRepoSearchResponse.prototype.getKnowledgeList = function () {
  return /** @type{!Array<!proto.kwaipilot_rag.Knowledge>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot_rag.Knowledge, 1)
  );
};

/**
 * @param {!Array<!proto.kwaipilot_rag.Knowledge>} value
 * @return {!proto.kwaipilot_rag.KnowledgeRepoSearchResponse} returns this
 */
proto.kwaipilot_rag.KnowledgeRepoSearchResponse.prototype.setKnowledgeList = function (value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};

/**
 * @param {!proto.kwaipilot_rag.Knowledge=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_rag.Knowledge}
 */
proto.kwaipilot_rag.KnowledgeRepoSearchResponse.prototype.addKnowledge = function (
  opt_value,
  opt_index
) {
  return jspb.Message.addToRepeatedWrapperField(
    this,
    1,
    opt_value,
    proto.kwaipilot_rag.Knowledge,
    opt_index
  );
};

/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_rag.KnowledgeRepoSearchResponse} returns this
 */
proto.kwaipilot_rag.KnowledgeRepoSearchResponse.prototype.clearKnowledgeList = function () {
  return this.setKnowledgeList([]);
};

/**
 * optional string prompt = 2;
 * @return {string}
 */
proto.kwaipilot_rag.KnowledgeRepoSearchResponse.prototype.getPrompt = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.KnowledgeRepoSearchResponse} returns this
 */
proto.kwaipilot_rag.KnowledgeRepoSearchResponse.prototype.setPrompt = function (value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};

if (jspb.Message.GENERATE_TO_OBJECT) {
  /**
   * Creates an object representation of this proto.
   * Field names that are reserved in JavaScript and will be renamed to pb_name.
   * Optional fields that are not set will be set to undefined.
   * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
   * For the list of reserved names please see:
   *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
   * @param {boolean=} opt_includeInstance Deprecated. whether to include the
   *     JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @return {!Object}
   */
  proto.kwaipilot_rag.ChatFile.prototype.toObject = function (opt_includeInstance) {
    return proto.kwaipilot_rag.ChatFile.toObject(opt_includeInstance, this);
  };

  /**
   * Static version of the {@see toObject} method.
   * @param {boolean|undefined} includeInstance Deprecated. Whether to include
   *     the JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @param {!proto.kwaipilot_rag.ChatFile} msg The msg instance to transform.
   * @return {!Object}
   * @suppress {unusedLocalVariables} f is only used for nested messages
   */
  proto.kwaipilot_rag.ChatFile.toObject = function (includeInstance, msg) {
    var f,
      obj = {
        code: jspb.Message.getFieldWithDefault(msg, 1, ''),
        name: jspb.Message.getFieldWithDefault(msg, 2, ''),
        language: jspb.Message.getFieldWithDefault(msg, 3, '')
      };

    if (includeInstance) {
      obj.$jspbMessageInstance = msg;
    }
    return obj;
  };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_rag.ChatFile}
 */
proto.kwaipilot_rag.ChatFile.deserializeBinary = function (bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_rag.ChatFile();
  return proto.kwaipilot_rag.ChatFile.deserializeBinaryFromReader(msg, reader);
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_rag.ChatFile} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_rag.ChatFile}
 */
proto.kwaipilot_rag.ChatFile.deserializeBinaryFromReader = function (msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
      case 1:
        var value = /** @type {string} */ (reader.readString());
        msg.setCode(value);
        break;
      case 2:
        var value = /** @type {string} */ (reader.readString());
        msg.setName(value);
        break;
      case 3:
        var value = /** @type {string} */ (reader.readString());
        msg.setLanguage(value);
        break;
      default:
        reader.skipField();
        break;
    }
  }
  return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_rag.ChatFile.prototype.serializeBinary = function () {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_rag.ChatFile.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_rag.ChatFile} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_rag.ChatFile.serializeBinaryToWriter = function (message, writer) {
  var f = undefined;
  f = message.getCode();
  if (f.length > 0) {
    writer.writeString(1, f);
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(2, f);
  }
  f = message.getLanguage();
  if (f.length > 0) {
    writer.writeString(3, f);
  }
};

/**
 * optional string code = 1;
 * @return {string}
 */
proto.kwaipilot_rag.ChatFile.prototype.getCode = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.ChatFile} returns this
 */
proto.kwaipilot_rag.ChatFile.prototype.setCode = function (value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};

/**
 * optional string name = 2;
 * @return {string}
 */
proto.kwaipilot_rag.ChatFile.prototype.getName = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.ChatFile} returns this
 */
proto.kwaipilot_rag.ChatFile.prototype.setName = function (value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};

/**
 * optional string language = 3;
 * @return {string}
 */
proto.kwaipilot_rag.ChatFile.prototype.getLanguage = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.ChatFile} returns this
 */
proto.kwaipilot_rag.ChatFile.prototype.setLanguage = function (value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};

if (jspb.Message.GENERATE_TO_OBJECT) {
  /**
   * Creates an object representation of this proto.
   * Field names that are reserved in JavaScript and will be renamed to pb_name.
   * Optional fields that are not set will be set to undefined.
   * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
   * For the list of reserved names please see:
   *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
   * @param {boolean=} opt_includeInstance Deprecated. whether to include the
   *     JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @return {!Object}
   */
  proto.kwaipilot_rag.CodeSearchData.prototype.toObject = function (opt_includeInstance) {
    return proto.kwaipilot_rag.CodeSearchData.toObject(opt_includeInstance, this);
  };

  /**
   * Static version of the {@see toObject} method.
   * @param {boolean|undefined} includeInstance Deprecated. Whether to include
   *     the JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @param {!proto.kwaipilot_rag.CodeSearchData} msg The msg instance to transform.
   * @return {!Object}
   * @suppress {unusedLocalVariables} f is only used for nested messages
   */
  proto.kwaipilot_rag.CodeSearchData.toObject = function (includeInstance, msg) {
    var f,
      obj = {
        id: jspb.Message.getFieldWithDefault(msg, 1, 0),
        path: jspb.Message.getFieldWithDefault(msg, 2, ''),
        startLineNo: jspb.Message.getFieldWithDefault(msg, 3, 0),
        endLineNo: jspb.Message.getFieldWithDefault(msg, 4, 0),
        startColNo: jspb.Message.getFieldWithDefault(msg, 5, 0),
        endColNo: jspb.Message.getFieldWithDefault(msg, 6, 0),
        code: jspb.Message.getFieldWithDefault(msg, 7, ''),
        language: jspb.Message.getFieldWithDefault(msg, 8, ''),
        functionName: jspb.Message.getFieldWithDefault(msg, 9, ''),
        functionSignature: jspb.Message.getFieldWithDefault(msg, 10, ''),
        codeType: jspb.Message.getFieldWithDefault(msg, 11, ''),
        repoName: jspb.Message.getFieldWithDefault(msg, 12, '')
      };

    if (includeInstance) {
      obj.$jspbMessageInstance = msg;
    }
    return obj;
  };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_rag.CodeSearchData}
 */
proto.kwaipilot_rag.CodeSearchData.deserializeBinary = function (bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_rag.CodeSearchData();
  return proto.kwaipilot_rag.CodeSearchData.deserializeBinaryFromReader(msg, reader);
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_rag.CodeSearchData} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_rag.CodeSearchData}
 */
proto.kwaipilot_rag.CodeSearchData.deserializeBinaryFromReader = function (msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
      case 1:
        var value = /** @type {number} */ (reader.readInt64());
        msg.setId(value);
        break;
      case 2:
        var value = /** @type {string} */ (reader.readString());
        msg.setPath(value);
        break;
      case 3:
        var value = /** @type {number} */ (reader.readInt64());
        msg.setStartLineNo(value);
        break;
      case 4:
        var value = /** @type {number} */ (reader.readInt64());
        msg.setEndLineNo(value);
        break;
      case 5:
        var value = /** @type {number} */ (reader.readInt64());
        msg.setStartColNo(value);
        break;
      case 6:
        var value = /** @type {number} */ (reader.readInt64());
        msg.setEndColNo(value);
        break;
      case 7:
        var value = /** @type {string} */ (reader.readString());
        msg.setCode(value);
        break;
      case 8:
        var value = /** @type {string} */ (reader.readString());
        msg.setLanguage(value);
        break;
      case 9:
        var value = /** @type {string} */ (reader.readString());
        msg.setFunctionName(value);
        break;
      case 10:
        var value = /** @type {string} */ (reader.readString());
        msg.setFunctionSignature(value);
        break;
      case 11:
        var value = /** @type {string} */ (reader.readString());
        msg.setCodeType(value);
        break;
      case 12:
        var value = /** @type {string} */ (reader.readString());
        msg.setRepoName(value);
        break;
      default:
        reader.skipField();
        break;
    }
  }
  return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_rag.CodeSearchData.prototype.serializeBinary = function () {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_rag.CodeSearchData.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_rag.CodeSearchData} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_rag.CodeSearchData.serializeBinaryToWriter = function (message, writer) {
  var f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt64(1, f);
  }
  f = message.getPath();
  if (f.length > 0) {
    writer.writeString(2, f);
  }
  f = message.getStartLineNo();
  if (f !== 0) {
    writer.writeInt64(3, f);
  }
  f = message.getEndLineNo();
  if (f !== 0) {
    writer.writeInt64(4, f);
  }
  f = message.getStartColNo();
  if (f !== 0) {
    writer.writeInt64(5, f);
  }
  f = message.getEndColNo();
  if (f !== 0) {
    writer.writeInt64(6, f);
  }
  f = message.getCode();
  if (f.length > 0) {
    writer.writeString(7, f);
  }
  f = message.getLanguage();
  if (f.length > 0) {
    writer.writeString(8, f);
  }
  f = message.getFunctionName();
  if (f.length > 0) {
    writer.writeString(9, f);
  }
  f = message.getFunctionSignature();
  if (f.length > 0) {
    writer.writeString(10, f);
  }
  f = message.getCodeType();
  if (f.length > 0) {
    writer.writeString(11, f);
  }
  f = message.getRepoName();
  if (f.length > 0) {
    writer.writeString(12, f);
  }
};

/**
 * optional int64 id = 1;
 * @return {number}
 */
proto.kwaipilot_rag.CodeSearchData.prototype.getId = function () {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};

/**
 * @param {number} value
 * @return {!proto.kwaipilot_rag.CodeSearchData} returns this
 */
proto.kwaipilot_rag.CodeSearchData.prototype.setId = function (value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};

/**
 * optional string path = 2;
 * @return {string}
 */
proto.kwaipilot_rag.CodeSearchData.prototype.getPath = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.CodeSearchData} returns this
 */
proto.kwaipilot_rag.CodeSearchData.prototype.setPath = function (value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};

/**
 * optional int64 start_line_no = 3;
 * @return {number}
 */
proto.kwaipilot_rag.CodeSearchData.prototype.getStartLineNo = function () {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};

/**
 * @param {number} value
 * @return {!proto.kwaipilot_rag.CodeSearchData} returns this
 */
proto.kwaipilot_rag.CodeSearchData.prototype.setStartLineNo = function (value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};

/**
 * optional int64 end_line_no = 4;
 * @return {number}
 */
proto.kwaipilot_rag.CodeSearchData.prototype.getEndLineNo = function () {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};

/**
 * @param {number} value
 * @return {!proto.kwaipilot_rag.CodeSearchData} returns this
 */
proto.kwaipilot_rag.CodeSearchData.prototype.setEndLineNo = function (value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};

/**
 * optional int64 start_col_no = 5;
 * @return {number}
 */
proto.kwaipilot_rag.CodeSearchData.prototype.getStartColNo = function () {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};

/**
 * @param {number} value
 * @return {!proto.kwaipilot_rag.CodeSearchData} returns this
 */
proto.kwaipilot_rag.CodeSearchData.prototype.setStartColNo = function (value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};

/**
 * optional int64 end_col_no = 6;
 * @return {number}
 */
proto.kwaipilot_rag.CodeSearchData.prototype.getEndColNo = function () {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};

/**
 * @param {number} value
 * @return {!proto.kwaipilot_rag.CodeSearchData} returns this
 */
proto.kwaipilot_rag.CodeSearchData.prototype.setEndColNo = function (value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};

/**
 * optional string code = 7;
 * @return {string}
 */
proto.kwaipilot_rag.CodeSearchData.prototype.getCode = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.CodeSearchData} returns this
 */
proto.kwaipilot_rag.CodeSearchData.prototype.setCode = function (value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};

/**
 * optional string language = 8;
 * @return {string}
 */
proto.kwaipilot_rag.CodeSearchData.prototype.getLanguage = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.CodeSearchData} returns this
 */
proto.kwaipilot_rag.CodeSearchData.prototype.setLanguage = function (value) {
  return jspb.Message.setProto3StringField(this, 8, value);
};

/**
 * optional string function_name = 9;
 * @return {string}
 */
proto.kwaipilot_rag.CodeSearchData.prototype.getFunctionName = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.CodeSearchData} returns this
 */
proto.kwaipilot_rag.CodeSearchData.prototype.setFunctionName = function (value) {
  return jspb.Message.setProto3StringField(this, 9, value);
};

/**
 * optional string function_signature = 10;
 * @return {string}
 */
proto.kwaipilot_rag.CodeSearchData.prototype.getFunctionSignature = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.CodeSearchData} returns this
 */
proto.kwaipilot_rag.CodeSearchData.prototype.setFunctionSignature = function (value) {
  return jspb.Message.setProto3StringField(this, 10, value);
};

/**
 * optional string code_type = 11;
 * @return {string}
 */
proto.kwaipilot_rag.CodeSearchData.prototype.getCodeType = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.CodeSearchData} returns this
 */
proto.kwaipilot_rag.CodeSearchData.prototype.setCodeType = function (value) {
  return jspb.Message.setProto3StringField(this, 11, value);
};

/**
 * optional string repo_name = 12;
 * @return {string}
 */
proto.kwaipilot_rag.CodeSearchData.prototype.getRepoName = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 12, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.CodeSearchData} returns this
 */
proto.kwaipilot_rag.CodeSearchData.prototype.setRepoName = function (value) {
  return jspb.Message.setProto3StringField(this, 12, value);
};

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot_rag.CodeSearchRequest.repeatedFields_ = [3, 5, 7, 8];

if (jspb.Message.GENERATE_TO_OBJECT) {
  /**
   * Creates an object representation of this proto.
   * Field names that are reserved in JavaScript and will be renamed to pb_name.
   * Optional fields that are not set will be set to undefined.
   * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
   * For the list of reserved names please see:
   *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
   * @param {boolean=} opt_includeInstance Deprecated. whether to include the
   *     JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @return {!Object}
   */
  proto.kwaipilot_rag.CodeSearchRequest.prototype.toObject = function (opt_includeInstance) {
    return proto.kwaipilot_rag.CodeSearchRequest.toObject(opt_includeInstance, this);
  };

  /**
   * Static version of the {@see toObject} method.
   * @param {boolean|undefined} includeInstance Deprecated. Whether to include
   *     the JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @param {!proto.kwaipilot_rag.CodeSearchRequest} msg The msg instance to transform.
   * @return {!Object}
   * @suppress {unusedLocalVariables} f is only used for nested messages
   */
  proto.kwaipilot_rag.CodeSearchRequest.toObject = function (includeInstance, msg) {
    var f,
      obj = {
        repoName: jspb.Message.getFieldWithDefault(msg, 1, ''),
        commitId: jspb.Message.getFieldWithDefault(msg, 2, ''),
        targetDirectoryList: (f = jspb.Message.getRepeatedField(msg, 3)) == null ? undefined : f,
        query: jspb.Message.getFieldWithDefault(msg, 4, ''),
        filesList: jspb.Message.toObjectList(
          msg.getFilesList(),
          proto.kwaipilot_rag.ChatFile.toObject,
          includeInstance
        ),
        username: jspb.Message.getFieldWithDefault(msg, 6, ''),
        repoNamesList: (f = jspb.Message.getRepeatedField(msg, 7)) == null ? undefined : f,
        commitIdsList: (f = jspb.Message.getRepeatedField(msg, 8)) == null ? undefined : f,
        topK: jspb.Message.getFieldWithDefault(msg, 9, 0),
        threshold: jspb.Message.getFloatingPointFieldWithDefault(msg, 10, 0.0)
      };

    if (includeInstance) {
      obj.$jspbMessageInstance = msg;
    }
    return obj;
  };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_rag.CodeSearchRequest}
 */
proto.kwaipilot_rag.CodeSearchRequest.deserializeBinary = function (bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_rag.CodeSearchRequest();
  return proto.kwaipilot_rag.CodeSearchRequest.deserializeBinaryFromReader(msg, reader);
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_rag.CodeSearchRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_rag.CodeSearchRequest}
 */
proto.kwaipilot_rag.CodeSearchRequest.deserializeBinaryFromReader = function (msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
      case 1:
        var value = /** @type {string} */ (reader.readString());
        msg.setRepoName(value);
        break;
      case 2:
        var value = /** @type {string} */ (reader.readString());
        msg.setCommitId(value);
        break;
      case 3:
        var value = /** @type {string} */ (reader.readString());
        msg.addTargetDirectory(value);
        break;
      case 4:
        var value = /** @type {string} */ (reader.readString());
        msg.setQuery(value);
        break;
      case 5:
        var value = new proto.kwaipilot_rag.ChatFile();
        reader.readMessage(value, proto.kwaipilot_rag.ChatFile.deserializeBinaryFromReader);
        msg.addFiles(value);
        break;
      case 6:
        var value = /** @type {string} */ (reader.readString());
        msg.setUsername(value);
        break;
      case 7:
        var value = /** @type {string} */ (reader.readString());
        msg.addRepoNames(value);
        break;
      case 8:
        var value = /** @type {string} */ (reader.readString());
        msg.addCommitIds(value);
        break;
      case 9:
        var value = /** @type {number} */ (reader.readInt32());
        msg.setTopK(value);
        break;
      case 10:
        var value = /** @type {number} */ (reader.readDouble());
        msg.setThreshold(value);
        break;
      default:
        reader.skipField();
        break;
    }
  }
  return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.serializeBinary = function () {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_rag.CodeSearchRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_rag.CodeSearchRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_rag.CodeSearchRequest.serializeBinaryToWriter = function (message, writer) {
  var f = undefined;
  f = message.getRepoName();
  if (f.length > 0) {
    writer.writeString(1, f);
  }
  f = message.getCommitId();
  if (f.length > 0) {
    writer.writeString(2, f);
  }
  f = message.getTargetDirectoryList();
  if (f.length > 0) {
    writer.writeRepeatedString(3, f);
  }
  f = message.getQuery();
  if (f.length > 0) {
    writer.writeString(4, f);
  }
  f = message.getFilesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(5, f, proto.kwaipilot_rag.ChatFile.serializeBinaryToWriter);
  }
  f = message.getUsername();
  if (f.length > 0) {
    writer.writeString(6, f);
  }
  f = message.getRepoNamesList();
  if (f.length > 0) {
    writer.writeRepeatedString(7, f);
  }
  f = message.getCommitIdsList();
  if (f.length > 0) {
    writer.writeRepeatedString(8, f);
  }
  f = message.getTopK();
  if (f !== 0) {
    writer.writeInt32(9, f);
  }
  f = message.getThreshold();
  if (f !== 0.0) {
    writer.writeDouble(10, f);
  }
};

/**
 * optional string repo_name = 1;
 * @return {string}
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.getRepoName = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.CodeSearchRequest} returns this
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.setRepoName = function (value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};

/**
 * optional string commit_id = 2;
 * @return {string}
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.getCommitId = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.CodeSearchRequest} returns this
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.setCommitId = function (value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};

/**
 * repeated string target_directory = 3;
 * @return {!Array<string>}
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.getTargetDirectoryList = function () {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 3));
};

/**
 * @param {!Array<string>} value
 * @return {!proto.kwaipilot_rag.CodeSearchRequest} returns this
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.setTargetDirectoryList = function (value) {
  return jspb.Message.setField(this, 3, value || []);
};

/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_rag.CodeSearchRequest} returns this
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.addTargetDirectory = function (value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 3, value, opt_index);
};

/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_rag.CodeSearchRequest} returns this
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.clearTargetDirectoryList = function () {
  return this.setTargetDirectoryList([]);
};

/**
 * optional string query = 4;
 * @return {string}
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.getQuery = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.CodeSearchRequest} returns this
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.setQuery = function (value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};

/**
 * repeated ChatFile files = 5;
 * @return {!Array<!proto.kwaipilot_rag.ChatFile>}
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.getFilesList = function () {
  return /** @type{!Array<!proto.kwaipilot_rag.ChatFile>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot_rag.ChatFile, 5)
  );
};

/**
 * @param {!Array<!proto.kwaipilot_rag.ChatFile>} value
 * @return {!proto.kwaipilot_rag.CodeSearchRequest} returns this
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.setFilesList = function (value) {
  return jspb.Message.setRepeatedWrapperField(this, 5, value);
};

/**
 * @param {!proto.kwaipilot_rag.ChatFile=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_rag.ChatFile}
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.addFiles = function (opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(
    this,
    5,
    opt_value,
    proto.kwaipilot_rag.ChatFile,
    opt_index
  );
};

/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_rag.CodeSearchRequest} returns this
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.clearFilesList = function () {
  return this.setFilesList([]);
};

/**
 * optional string username = 6;
 * @return {string}
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.getUsername = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.CodeSearchRequest} returns this
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.setUsername = function (value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};

/**
 * repeated string repo_names = 7;
 * @return {!Array<string>}
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.getRepoNamesList = function () {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 7));
};

/**
 * @param {!Array<string>} value
 * @return {!proto.kwaipilot_rag.CodeSearchRequest} returns this
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.setRepoNamesList = function (value) {
  return jspb.Message.setField(this, 7, value || []);
};

/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_rag.CodeSearchRequest} returns this
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.addRepoNames = function (value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 7, value, opt_index);
};

/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_rag.CodeSearchRequest} returns this
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.clearRepoNamesList = function () {
  return this.setRepoNamesList([]);
};

/**
 * repeated string commit_ids = 8;
 * @return {!Array<string>}
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.getCommitIdsList = function () {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 8));
};

/**
 * @param {!Array<string>} value
 * @return {!proto.kwaipilot_rag.CodeSearchRequest} returns this
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.setCommitIdsList = function (value) {
  return jspb.Message.setField(this, 8, value || []);
};

/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_rag.CodeSearchRequest} returns this
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.addCommitIds = function (value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 8, value, opt_index);
};

/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_rag.CodeSearchRequest} returns this
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.clearCommitIdsList = function () {
  return this.setCommitIdsList([]);
};

/**
 * optional int32 top_k = 9;
 * @return {number}
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.getTopK = function () {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};

/**
 * @param {number} value
 * @return {!proto.kwaipilot_rag.CodeSearchRequest} returns this
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.setTopK = function (value) {
  return jspb.Message.setProto3IntField(this, 9, value);
};

/**
 * optional double threshold = 10;
 * @return {number}
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.getThreshold = function () {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 10, 0.0));
};

/**
 * @param {number} value
 * @return {!proto.kwaipilot_rag.CodeSearchRequest} returns this
 */
proto.kwaipilot_rag.CodeSearchRequest.prototype.setThreshold = function (value) {
  return jspb.Message.setProto3FloatField(this, 10, value);
};

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot_rag.CodeSearchResponse.repeatedFields_ = [1];

if (jspb.Message.GENERATE_TO_OBJECT) {
  /**
   * Creates an object representation of this proto.
   * Field names that are reserved in JavaScript and will be renamed to pb_name.
   * Optional fields that are not set will be set to undefined.
   * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
   * For the list of reserved names please see:
   *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
   * @param {boolean=} opt_includeInstance Deprecated. whether to include the
   *     JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @return {!Object}
   */
  proto.kwaipilot_rag.CodeSearchResponse.prototype.toObject = function (opt_includeInstance) {
    return proto.kwaipilot_rag.CodeSearchResponse.toObject(opt_includeInstance, this);
  };

  /**
   * Static version of the {@see toObject} method.
   * @param {boolean|undefined} includeInstance Deprecated. Whether to include
   *     the JSPB instance for transitional soy proto support:
   *     http://goto/soy-param-migration
   * @param {!proto.kwaipilot_rag.CodeSearchResponse} msg The msg instance to transform.
   * @return {!Object}
   * @suppress {unusedLocalVariables} f is only used for nested messages
   */
  proto.kwaipilot_rag.CodeSearchResponse.toObject = function (includeInstance, msg) {
    var f,
      obj = {
        listList: jspb.Message.toObjectList(
          msg.getListList(),
          proto.kwaipilot_rag.CodeSearchData.toObject,
          includeInstance
        ),
        prompt: jspb.Message.getFieldWithDefault(msg, 2, ''),
        total: jspb.Message.getFieldWithDefault(msg, 3, 0),
        status: jspb.Message.getFieldWithDefault(msg, 4, 0)
      };

    if (includeInstance) {
      obj.$jspbMessageInstance = msg;
    }
    return obj;
  };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_rag.CodeSearchResponse}
 */
proto.kwaipilot_rag.CodeSearchResponse.deserializeBinary = function (bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_rag.CodeSearchResponse();
  return proto.kwaipilot_rag.CodeSearchResponse.deserializeBinaryFromReader(msg, reader);
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_rag.CodeSearchResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_rag.CodeSearchResponse}
 */
proto.kwaipilot_rag.CodeSearchResponse.deserializeBinaryFromReader = function (msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
      case 1:
        var value = new proto.kwaipilot_rag.CodeSearchData();
        reader.readMessage(value, proto.kwaipilot_rag.CodeSearchData.deserializeBinaryFromReader);
        msg.addList(value);
        break;
      case 2:
        var value = /** @type {string} */ (reader.readString());
        msg.setPrompt(value);
        break;
      case 3:
        var value = /** @type {number} */ (reader.readInt32());
        msg.setTotal(value);
        break;
      case 4:
        var value = /** @type {number} */ (reader.readInt32());
        msg.setStatus(value);
        break;
      default:
        reader.skipField();
        break;
    }
  }
  return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_rag.CodeSearchResponse.prototype.serializeBinary = function () {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_rag.CodeSearchResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_rag.CodeSearchResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_rag.CodeSearchResponse.serializeBinaryToWriter = function (message, writer) {
  var f = undefined;
  f = message.getListList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(1, f, proto.kwaipilot_rag.CodeSearchData.serializeBinaryToWriter);
  }
  f = message.getPrompt();
  if (f.length > 0) {
    writer.writeString(2, f);
  }
  f = message.getTotal();
  if (f !== 0) {
    writer.writeInt32(3, f);
  }
  f = message.getStatus();
  if (f !== 0) {
    writer.writeInt32(4, f);
  }
};

/**
 * repeated CodeSearchData list = 1;
 * @return {!Array<!proto.kwaipilot_rag.CodeSearchData>}
 */
proto.kwaipilot_rag.CodeSearchResponse.prototype.getListList = function () {
  return /** @type{!Array<!proto.kwaipilot_rag.CodeSearchData>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot_rag.CodeSearchData, 1)
  );
};

/**
 * @param {!Array<!proto.kwaipilot_rag.CodeSearchData>} value
 * @return {!proto.kwaipilot_rag.CodeSearchResponse} returns this
 */
proto.kwaipilot_rag.CodeSearchResponse.prototype.setListList = function (value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};

/**
 * @param {!proto.kwaipilot_rag.CodeSearchData=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_rag.CodeSearchData}
 */
proto.kwaipilot_rag.CodeSearchResponse.prototype.addList = function (opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(
    this,
    1,
    opt_value,
    proto.kwaipilot_rag.CodeSearchData,
    opt_index
  );
};

/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_rag.CodeSearchResponse} returns this
 */
proto.kwaipilot_rag.CodeSearchResponse.prototype.clearListList = function () {
  return this.setListList([]);
};

/**
 * optional string prompt = 2;
 * @return {string}
 */
proto.kwaipilot_rag.CodeSearchResponse.prototype.getPrompt = function () {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ''));
};

/**
 * @param {string} value
 * @return {!proto.kwaipilot_rag.CodeSearchResponse} returns this
 */
proto.kwaipilot_rag.CodeSearchResponse.prototype.setPrompt = function (value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};

/**
 * optional int32 total = 3;
 * @return {number}
 */
proto.kwaipilot_rag.CodeSearchResponse.prototype.getTotal = function () {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};

/**
 * @param {number} value
 * @return {!proto.kwaipilot_rag.CodeSearchResponse} returns this
 */
proto.kwaipilot_rag.CodeSearchResponse.prototype.setTotal = function (value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};

/**
 * optional int32 status = 4;
 * @return {number}
 */
proto.kwaipilot_rag.CodeSearchResponse.prototype.getStatus = function () {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};

/**
 * @param {number} value
 * @return {!proto.kwaipilot_rag.CodeSearchResponse} returns this
 */
proto.kwaipilot_rag.CodeSearchResponse.prototype.setStatus = function (value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};

goog.object.extend(exports, proto.kwaipilot_rag);
