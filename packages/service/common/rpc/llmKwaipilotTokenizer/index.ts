import { RPC, GRPCProtocol, KessRegistry, Consumer, Metadata } from '@infra-node/rpc';
import { IConsumer } from './proto/llm_tokenizer_grpc_pb';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { KWS_LANE_ID } from '@fastgpt/global/core/chat/constants';

const logger = createLogger('llm-tokenizer');
const TIMEOUT = 1000;

const registry = new KessRegistry({
  balance: 'round-robin',
  serviceName: 'grpc_python_kwaipilot_tokenizer'
});

const protocol = new GRPCProtocol({
  methodLowerCamelCase: true,
  protoLoader: {
    enums: Number,
    oneofs: false
  },
  proto: require('./proto/llm_tokenizer_grpc_pb')
});

let consumer: Consumer<IConsumer> | null = null;
export async function getLLMTokenizerConsumer() {
  if (!consumer) {
    consumer = await RPC.createConsumer<IConsumer>({
      registry,
      protocol,
      timeout: TIMEOUT
    });
  }
  return consumer;
}

export function getMetadata() {
  const metadata = new Metadata();
  if (KWS_LANE_ID) {
    metadata.set('lane_id', KWS_LANE_ID);
    logger.info(`set lane_id: ${KWS_LANE_ID}`);
  }
  return metadata;
}

export * from './proto/llm_tokenizer_pb';
