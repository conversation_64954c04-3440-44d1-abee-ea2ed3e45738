syntax = "proto3";

package kwaipilot_llm_api;
option java_multiple_files = true;

message KwaipilotLlmRequest {
    string request_body_json_str = 1;
}

message KwaipilotLlmResponse {
    string response_json_str = 1;
}

message KwaipilotTokenIds {
    repeated int32 token_ids = 1;
}

message Kwai<PERSON>lotEmbeddingRequest {
    repeated string texts = 1;
}

message Embedding {
    repeated float embedding = 1;
}

message EmbeddingResult {
    map <string, Embedding> embedding_result = 1;
}

message KwaipilotEmbeddingResponse {
    repeated EmbeddingResult embedding_results = 1;
}

message KwaipilotRerankingRequest {
    string query = 1;
    repeated string documents = 2;
}

message KwaipilotRerankingResult {
    repeated float scores = 1;
}

message KwaipilotCodeEditPredictRequest {
    repeated string historys = 1;
    string language = 2;
    int32 cursor_offset = 4;
}

message CodeEdit{
    string search = 1;
    string replace = 2;
}

message KwaipilotCodeEditPredictResult {
    repeated CodeEdit search_replaces = 1;
}

message File{
    string file_path = 1;
    string file_content = 2;
}

message KwaipilotInstantApplyRequest {
    repeated File related_files = 1;
    string model_output = 2;
    string instruction = 3;
    string language = 4;
}

service KwaipilotLlmApiService {
    rpc TokenizerEncode(KwaipilotLlmRequest) returns (KwaipilotLlmRequest) {} // tokenize
    rpc TokenizerDecode(KwaipilotLlmRequest) returns (KwaipilotLlmRequest) {} // detokenize
}