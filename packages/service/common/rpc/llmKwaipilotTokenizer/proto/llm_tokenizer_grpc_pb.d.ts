// package: kwaipilot_llm_api
// file: llm_tokenizer.proto

/* tslint:disable */
/* eslint-disable */

import * as grpc from '@infra-node/grpc-js';
import * as llm_tokenizer_pb from './llm_tokenizer_pb';
import type { Metadata, RPCCallOptions } from '@infra-node/rpc';

export interface IKwaipilotLlmApiServiceClient {
  tokenizerEncode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_tokenizer_pb.KwaipilotLlmRequest
    ) => void
  ): void;
  tokenizerEncode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_tokenizer_pb.KwaipilotLlmRequest
    ) => void
  ): void;
  tokenizerEncode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest,
    metadata: Metadata,
    options: RPC<PERSON>allOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_tokenizer_pb.KwaipilotLlmRequest
    ) => void
  ): void;
  tokenizerDecode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_tokenizer_pb.KwaipilotLlmRequest
    ) => void
  ): void;
  tokenizerDecode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_tokenizer_pb.KwaipilotLlmRequest
    ) => void
  ): void;
  tokenizerDecode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_tokenizer_pb.KwaipilotLlmRequest
    ) => void
  ): void;
}

export interface IKwaipilotLlmApiServiceClientPromisify {
  tokenizerEncode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest
  ): Promise<llm_tokenizer_pb.KwaipilotLlmRequest>;
  tokenizerEncode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest,
    metadata: Metadata
  ): Promise<llm_tokenizer_pb.KwaipilotLlmRequest>;
  tokenizerEncode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_tokenizer_pb.KwaipilotLlmRequest>;
  tokenizerDecode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest
  ): Promise<llm_tokenizer_pb.KwaipilotLlmRequest>;
  tokenizerDecode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest,
    metadata: Metadata
  ): Promise<llm_tokenizer_pb.KwaipilotLlmRequest>;
  tokenizerDecode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_tokenizer_pb.KwaipilotLlmRequest>;
}

export interface IKwaipilotLlmApiServiceClientDynamic {
  tokenizerEncode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject
    ) => void
  ): void;
  tokenizerEncode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject
    ) => void
  ): void;
  tokenizerEncode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject
    ) => void
  ): void;
  tokenizerDecode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject
    ) => void
  ): void;
  tokenizerDecode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject
    ) => void
  ): void;
  tokenizerDecode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject
    ) => void
  ): void;
}

export interface IKwaipilotLlmApiServiceClientDynamicPromisify {
  tokenizerEncode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject
  ): Promise<llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject>;
  tokenizerEncode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject,
    metadata: Metadata
  ): Promise<llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject>;
  tokenizerEncode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject>;
  tokenizerDecode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject
  ): Promise<llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject>;
  tokenizerDecode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject,
    metadata: Metadata
  ): Promise<llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject>;
  tokenizerDecode(
    request: llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_tokenizer_pb.KwaipilotLlmRequest.DynamicObject>;
}

export interface IConsumer {
  KwaipilotLlmApiService: IKwaipilotLlmApiServiceClient;
  __promise__: {
    KwaipilotLlmApiService: IKwaipilotLlmApiServiceClientPromisify;
  };
}

export interface IConsumerDynamic {
  KwaipilotLlmApiService: IKwaipilotLlmApiServiceClientDynamic;
  __promise__: {
    KwaipilotLlmApiService: IKwaipilotLlmApiServiceClientDynamicPromisify;
  };
}
