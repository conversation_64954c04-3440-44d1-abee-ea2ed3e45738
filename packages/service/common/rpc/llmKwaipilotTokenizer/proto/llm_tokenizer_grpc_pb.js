// GENERATED CODE -- DO NOT EDIT!

'use strict';
var grpc = require('@infra-node/grpc-js');
var llm_tokenizer_pb = require('./llm_tokenizer_pb.js');

function serialize_kwaipilot_llm_api_KwaipilotLlmRequest(arg) {
  if (!(arg instanceof llm_tokenizer_pb.KwaipilotLlmRequest)) {
    throw new Error('Expected argument of type kwaipilot_llm_api.KwaipilotLlmRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_llm_api_KwaipilotLlmRequest(buffer_arg) {
  return llm_tokenizer_pb.KwaipilotLlmRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

var KwaipilotLlmApiServiceService = (exports.KwaipilotLlmApiServiceService = {
  tokenizerEncode: {
    path: '/kwaipilot_llm_api.KwaipilotLlmApiService/TokenizerEncode',
    requestStream: false,
    responseStream: false,
    requestType: llm_tokenizer_pb.KwaipilotLlmRequest,
    responseType: llm_tokenizer_pb.KwaipilotLlmRequest,
    requestSerialize: serialize_kwaipilot_llm_api_KwaipilotLlmRequest,
    requestDeserialize: deserialize_kwaipilot_llm_api_KwaipilotLlmRequest,
    responseSerialize: serialize_kwaipilot_llm_api_KwaipilotLlmRequest,
    responseDeserialize: deserialize_kwaipilot_llm_api_KwaipilotLlmRequest
  },
  tokenizerDecode: {
    path: '/kwaipilot_llm_api.KwaipilotLlmApiService/TokenizerDecode',
    requestStream: false,
    responseStream: false,
    requestType: llm_tokenizer_pb.KwaipilotLlmRequest,
    responseType: llm_tokenizer_pb.KwaipilotLlmRequest,
    requestSerialize: serialize_kwaipilot_llm_api_KwaipilotLlmRequest,
    requestDeserialize: deserialize_kwaipilot_llm_api_KwaipilotLlmRequest,
    responseSerialize: serialize_kwaipilot_llm_api_KwaipilotLlmRequest,
    responseDeserialize: deserialize_kwaipilot_llm_api_KwaipilotLlmRequest
  }
});

exports.KwaipilotLlmApiServiceClient = grpc.makeGenericClientConstructor(
  KwaipilotLlmApiServiceService
);
