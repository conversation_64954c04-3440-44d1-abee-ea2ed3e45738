// package: kwaipilot_llm_api
// file: llm_tokenizer.proto

/* tslint:disable */
/* eslint-disable */

import * as jspb from 'google-protobuf';

export class KwaipilotLlmRequest extends jspb.Message {
  getRequestBodyJsonStr(): string;
  setRequestBodyJsonStr(value: string): KwaipilotLlmRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KwaipilotLlmRequest.AsObject;
  static toObject(includeInstance: boolean, msg: KwaipilotLlmRequest): KwaipilotLlmRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: Kwai<PERSON>lotLlmRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): KwaipilotLlmRequest;
  static deserializeBinaryFromReader(
    message: <PERSON>wai<PERSON>lotLlmRequest,
    reader: jspb.BinaryReader
  ): <PERSON>wai<PERSON>lotLlmRequest;
}

export namespace KwaipilotLlmRequest {
  export type AsObject = {
    requestBodyJsonStr: string;
  };

  export type DynamicObject = {
    requestBodyJsonStr: string;
  };
}

export class KwaipilotLlmResponse extends jspb.Message {
  getResponseJsonStr(): string;
  setResponseJsonStr(value: string): KwaipilotLlmResponse;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KwaipilotLlmResponse.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KwaipilotLlmResponse
  ): KwaipilotLlmResponse.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: KwaipilotLlmResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): KwaipilotLlmResponse;
  static deserializeBinaryFromReader(
    message: KwaipilotLlmResponse,
    reader: jspb.BinaryReader
  ): KwaipilotLlmResponse;
}

export namespace KwaipilotLlmResponse {
  export type AsObject = {
    responseJsonStr: string;
  };

  export type DynamicObject = {
    responseJsonStr: string;
  };
}

export class KwaipilotTokenIds extends jspb.Message {
  clearTokenIdsList(): void;
  getTokenIdsList(): Array<number>;
  setTokenIdsList(value: Array<number>): KwaipilotTokenIds;
  addTokenIds(value: number, index?: number): number;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KwaipilotTokenIds.AsObject;
  static toObject(includeInstance: boolean, msg: KwaipilotTokenIds): KwaipilotTokenIds.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: KwaipilotTokenIds, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): KwaipilotTokenIds;
  static deserializeBinaryFromReader(
    message: KwaipilotTokenIds,
    reader: jspb.BinaryReader
  ): KwaipilotTokenIds;
}

export namespace KwaipilotTokenIds {
  export type AsObject = {
    tokenIdsList: Array<number>;
  };

  export type DynamicObject = {
    tokenIds: Array<number>;
  };
}

export class KwaipilotEmbeddingRequest extends jspb.Message {
  clearTextsList(): void;
  getTextsList(): Array<string>;
  setTextsList(value: Array<string>): KwaipilotEmbeddingRequest;
  addTexts(value: string, index?: number): string;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KwaipilotEmbeddingRequest.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KwaipilotEmbeddingRequest
  ): KwaipilotEmbeddingRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KwaipilotEmbeddingRequest,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KwaipilotEmbeddingRequest;
  static deserializeBinaryFromReader(
    message: KwaipilotEmbeddingRequest,
    reader: jspb.BinaryReader
  ): KwaipilotEmbeddingRequest;
}

export namespace KwaipilotEmbeddingRequest {
  export type AsObject = {
    textsList: Array<string>;
  };

  export type DynamicObject = {
    texts: Array<string>;
  };
}

export class Embedding extends jspb.Message {
  clearEmbeddingList(): void;
  getEmbeddingList(): Array<number>;
  setEmbeddingList(value: Array<number>): Embedding;
  addEmbedding(value: number, index?: number): number;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Embedding.AsObject;
  static toObject(includeInstance: boolean, msg: Embedding): Embedding.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: Embedding, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Embedding;
  static deserializeBinaryFromReader(message: Embedding, reader: jspb.BinaryReader): Embedding;
}

export namespace Embedding {
  export type AsObject = {
    embeddingList: Array<number>;
  };

  export type DynamicObject = {
    embedding: Array<number>;
  };
}

export class EmbeddingResult extends jspb.Message {
  getEmbeddingResultMap(): jspb.Map<string, Embedding>;
  clearEmbeddingResultMap(): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): EmbeddingResult.AsObject;
  static toObject(includeInstance: boolean, msg: EmbeddingResult): EmbeddingResult.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: EmbeddingResult, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): EmbeddingResult;
  static deserializeBinaryFromReader(
    message: EmbeddingResult,
    reader: jspb.BinaryReader
  ): EmbeddingResult;
}

export namespace EmbeddingResult {
  export type AsObject = {
    embeddingResultMap: Array<[string, Embedding.AsObject]>;
  };

  export type DynamicObject = {
    embeddingResult: Record<string, Embedding.DynamicObject>;
  };
}

export class KwaipilotEmbeddingResponse extends jspb.Message {
  clearEmbeddingResultsList(): void;
  getEmbeddingResultsList(): Array<EmbeddingResult>;
  setEmbeddingResultsList(value: Array<EmbeddingResult>): KwaipilotEmbeddingResponse;
  addEmbeddingResults(value?: EmbeddingResult, index?: number): EmbeddingResult;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KwaipilotEmbeddingResponse.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KwaipilotEmbeddingResponse
  ): KwaipilotEmbeddingResponse.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KwaipilotEmbeddingResponse,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KwaipilotEmbeddingResponse;
  static deserializeBinaryFromReader(
    message: KwaipilotEmbeddingResponse,
    reader: jspb.BinaryReader
  ): KwaipilotEmbeddingResponse;
}

export namespace KwaipilotEmbeddingResponse {
  export type AsObject = {
    embeddingResultsList: Array<EmbeddingResult.AsObject>;
  };

  export type DynamicObject = {
    embeddingResults: Array<EmbeddingResult.DynamicObject>;
  };
}

export class KwaipilotRerankingRequest extends jspb.Message {
  getQuery(): string;
  setQuery(value: string): KwaipilotRerankingRequest;
  clearDocumentsList(): void;
  getDocumentsList(): Array<string>;
  setDocumentsList(value: Array<string>): KwaipilotRerankingRequest;
  addDocuments(value: string, index?: number): string;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KwaipilotRerankingRequest.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KwaipilotRerankingRequest
  ): KwaipilotRerankingRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KwaipilotRerankingRequest,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KwaipilotRerankingRequest;
  static deserializeBinaryFromReader(
    message: KwaipilotRerankingRequest,
    reader: jspb.BinaryReader
  ): KwaipilotRerankingRequest;
}

export namespace KwaipilotRerankingRequest {
  export type AsObject = {
    query: string;
    documentsList: Array<string>;
  };

  export type DynamicObject = {
    query: string;
    documents: Array<string>;
  };
}

export class KwaipilotRerankingResult extends jspb.Message {
  clearScoresList(): void;
  getScoresList(): Array<number>;
  setScoresList(value: Array<number>): KwaipilotRerankingResult;
  addScores(value: number, index?: number): number;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KwaipilotRerankingResult.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KwaipilotRerankingResult
  ): KwaipilotRerankingResult.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KwaipilotRerankingResult,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KwaipilotRerankingResult;
  static deserializeBinaryFromReader(
    message: KwaipilotRerankingResult,
    reader: jspb.BinaryReader
  ): KwaipilotRerankingResult;
}

export namespace KwaipilotRerankingResult {
  export type AsObject = {
    scoresList: Array<number>;
  };

  export type DynamicObject = {
    scores: Array<number>;
  };
}

export class KwaipilotCodeEditPredictRequest extends jspb.Message {
  clearHistorysList(): void;
  getHistorysList(): Array<string>;
  setHistorysList(value: Array<string>): KwaipilotCodeEditPredictRequest;
  addHistorys(value: string, index?: number): string;
  getLanguage(): string;
  setLanguage(value: string): KwaipilotCodeEditPredictRequest;
  getCursorOffset(): number;
  setCursorOffset(value: number): KwaipilotCodeEditPredictRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KwaipilotCodeEditPredictRequest.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KwaipilotCodeEditPredictRequest
  ): KwaipilotCodeEditPredictRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KwaipilotCodeEditPredictRequest,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KwaipilotCodeEditPredictRequest;
  static deserializeBinaryFromReader(
    message: KwaipilotCodeEditPredictRequest,
    reader: jspb.BinaryReader
  ): KwaipilotCodeEditPredictRequest;
}

export namespace KwaipilotCodeEditPredictRequest {
  export type AsObject = {
    historysList: Array<string>;
    language: string;
    cursorOffset: number;
  };

  export type DynamicObject = {
    historys: Array<string>;
    language: string;
    cursorOffset: number;
  };
}

export class CodeEdit extends jspb.Message {
  getSearch(): string;
  setSearch(value: string): CodeEdit;
  getReplace(): string;
  setReplace(value: string): CodeEdit;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CodeEdit.AsObject;
  static toObject(includeInstance: boolean, msg: CodeEdit): CodeEdit.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: CodeEdit, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CodeEdit;
  static deserializeBinaryFromReader(message: CodeEdit, reader: jspb.BinaryReader): CodeEdit;
}

export namespace CodeEdit {
  export type AsObject = {
    search: string;
    replace: string;
  };

  export type DynamicObject = {
    search: string;
    replace: string;
  };
}

export class KwaipilotCodeEditPredictResult extends jspb.Message {
  clearSearchReplacesList(): void;
  getSearchReplacesList(): Array<CodeEdit>;
  setSearchReplacesList(value: Array<CodeEdit>): KwaipilotCodeEditPredictResult;
  addSearchReplaces(value?: CodeEdit, index?: number): CodeEdit;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KwaipilotCodeEditPredictResult.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KwaipilotCodeEditPredictResult
  ): KwaipilotCodeEditPredictResult.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KwaipilotCodeEditPredictResult,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KwaipilotCodeEditPredictResult;
  static deserializeBinaryFromReader(
    message: KwaipilotCodeEditPredictResult,
    reader: jspb.BinaryReader
  ): KwaipilotCodeEditPredictResult;
}

export namespace KwaipilotCodeEditPredictResult {
  export type AsObject = {
    searchReplacesList: Array<CodeEdit.AsObject>;
  };

  export type DynamicObject = {
    searchReplaces: Array<CodeEdit.DynamicObject>;
  };
}

export class File extends jspb.Message {
  getFilePath(): string;
  setFilePath(value: string): File;
  getFileContent(): string;
  setFileContent(value: string): File;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): File.AsObject;
  static toObject(includeInstance: boolean, msg: File): File.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: File, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): File;
  static deserializeBinaryFromReader(message: File, reader: jspb.BinaryReader): File;
}

export namespace File {
  export type AsObject = {
    filePath: string;
    fileContent: string;
  };

  export type DynamicObject = {
    filePath: string;
    fileContent: string;
  };
}

export class KwaipilotInstantApplyRequest extends jspb.Message {
  clearRelatedFilesList(): void;
  getRelatedFilesList(): Array<File>;
  setRelatedFilesList(value: Array<File>): KwaipilotInstantApplyRequest;
  addRelatedFiles(value?: File, index?: number): File;
  getModelOutput(): string;
  setModelOutput(value: string): KwaipilotInstantApplyRequest;
  getInstruction(): string;
  setInstruction(value: string): KwaipilotInstantApplyRequest;
  getLanguage(): string;
  setLanguage(value: string): KwaipilotInstantApplyRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KwaipilotInstantApplyRequest.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KwaipilotInstantApplyRequest
  ): KwaipilotInstantApplyRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KwaipilotInstantApplyRequest,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KwaipilotInstantApplyRequest;
  static deserializeBinaryFromReader(
    message: KwaipilotInstantApplyRequest,
    reader: jspb.BinaryReader
  ): KwaipilotInstantApplyRequest;
}

export namespace KwaipilotInstantApplyRequest {
  export type AsObject = {
    relatedFilesList: Array<File.AsObject>;
    modelOutput: string;
    instruction: string;
    language: string;
  };

  export type DynamicObject = {
    relatedFiles: Array<File.DynamicObject>;
    modelOutput: string;
    instruction: string;
    language: string;
  };
}
