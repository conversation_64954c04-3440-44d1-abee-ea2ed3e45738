// source: llm_tokenizer.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.kwaipilot_llm_api.CodeEdit', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.Embedding', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.EmbeddingResult', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.File', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.KwaipilotLlmRequest', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.KwaipilotLlmResponse', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.KwaipilotRerankingRequest', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.KwaipilotRerankingResult', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.KwaipilotTokenIds', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot_llm_api.KwaipilotLlmRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.KwaipilotLlmRequest.displayName = 'proto.kwaipilot_llm_api.KwaipilotLlmRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot_llm_api.KwaipilotLlmResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.KwaipilotLlmResponse.displayName = 'proto.kwaipilot_llm_api.KwaipilotLlmResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot_llm_api.KwaipilotTokenIds.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot_llm_api.KwaipilotTokenIds, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.KwaipilotTokenIds.displayName = 'proto.kwaipilot_llm_api.KwaipilotTokenIds';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.displayName = 'proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.Embedding = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot_llm_api.Embedding.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot_llm_api.Embedding, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.Embedding.displayName = 'proto.kwaipilot_llm_api.Embedding';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.EmbeddingResult = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot_llm_api.EmbeddingResult, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.EmbeddingResult.displayName = 'proto.kwaipilot_llm_api.EmbeddingResult';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.displayName = 'proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.KwaipilotRerankingRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot_llm_api.KwaipilotRerankingRequest.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot_llm_api.KwaipilotRerankingRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.KwaipilotRerankingRequest.displayName = 'proto.kwaipilot_llm_api.KwaipilotRerankingRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.KwaipilotRerankingResult = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot_llm_api.KwaipilotRerankingResult.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot_llm_api.KwaipilotRerankingResult, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.KwaipilotRerankingResult.displayName = 'proto.kwaipilot_llm_api.KwaipilotRerankingResult';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.displayName = 'proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.CodeEdit = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot_llm_api.CodeEdit, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.CodeEdit.displayName = 'proto.kwaipilot_llm_api.CodeEdit';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult.displayName = 'proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.File = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot_llm_api.File, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.File.displayName = 'proto.kwaipilot_llm_api.File';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.displayName = 'proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot_llm_api.KwaipilotLlmRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot_llm_api.KwaipilotLlmRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    requestBodyJsonStr: jspb.Message.getFieldWithDefault(msg, 1, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.KwaipilotLlmRequest}
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.KwaipilotLlmRequest;
  return proto.kwaipilot_llm_api.KwaipilotLlmRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.KwaipilotLlmRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.KwaipilotLlmRequest}
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setRequestBodyJsonStr(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.KwaipilotLlmRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.KwaipilotLlmRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRequestBodyJsonStr();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
};


/**
 * optional string request_body_json_str = 1;
 * @return {string}
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.prototype.getRequestBodyJsonStr = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotLlmRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.prototype.setRequestBodyJsonStr = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot_llm_api.KwaipilotLlmResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot_llm_api.KwaipilotLlmResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    responseJsonStr: jspb.Message.getFieldWithDefault(msg, 1, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.KwaipilotLlmResponse}
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.KwaipilotLlmResponse;
  return proto.kwaipilot_llm_api.KwaipilotLlmResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.KwaipilotLlmResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.KwaipilotLlmResponse}
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setResponseJsonStr(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.KwaipilotLlmResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.KwaipilotLlmResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getResponseJsonStr();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
};


/**
 * optional string response_json_str = 1;
 * @return {string}
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.prototype.getResponseJsonStr = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotLlmResponse} returns this
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.prototype.setResponseJsonStr = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot_llm_api.KwaipilotTokenIds.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot_llm_api.KwaipilotTokenIds} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.toObject = function(includeInstance, msg) {
  var f, obj = {
    tokenIdsList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.KwaipilotTokenIds}
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.KwaipilotTokenIds;
  return proto.kwaipilot_llm_api.KwaipilotTokenIds.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.KwaipilotTokenIds} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.KwaipilotTokenIds}
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedInt32() : [reader.readInt32()]);
      for (var i = 0; i < values.length; i++) {
        msg.addTokenIds(values[i]);
      }
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.KwaipilotTokenIds.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.KwaipilotTokenIds} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTokenIdsList();
  if (f.length > 0) {
    writer.writePackedInt32(
      1,
      f
    );
  }
};


/**
 * repeated int32 token_ids = 1;
 * @return {!Array<number>}
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.prototype.getTokenIdsList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotTokenIds} returns this
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.prototype.setTokenIdsList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_llm_api.KwaipilotTokenIds} returns this
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.prototype.addTokenIds = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_llm_api.KwaipilotTokenIds} returns this
 */
proto.kwaipilot_llm_api.KwaipilotTokenIds.prototype.clearTokenIdsList = function() {
  return this.setTokenIdsList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    textsList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest;
  return proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.addTexts(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTextsList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      1,
      f
    );
  }
};


/**
 * repeated string texts = 1;
 * @return {!Array<string>}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.prototype.getTextsList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.prototype.setTextsList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.prototype.addTexts = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingRequest.prototype.clearTextsList = function() {
  return this.setTextsList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot_llm_api.Embedding.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot_llm_api.Embedding.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot_llm_api.Embedding.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot_llm_api.Embedding} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.Embedding.toObject = function(includeInstance, msg) {
  var f, obj = {
    embeddingList: (f = jspb.Message.getRepeatedFloatingPointField(msg, 1)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.Embedding}
 */
proto.kwaipilot_llm_api.Embedding.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.Embedding;
  return proto.kwaipilot_llm_api.Embedding.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.Embedding} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.Embedding}
 */
proto.kwaipilot_llm_api.Embedding.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedFloat() : [reader.readFloat()]);
      for (var i = 0; i < values.length; i++) {
        msg.addEmbedding(values[i]);
      }
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.Embedding.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.Embedding.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.Embedding} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.Embedding.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getEmbeddingList();
  if (f.length > 0) {
    writer.writePackedFloat(
      1,
      f
    );
  }
};


/**
 * repeated float embedding = 1;
 * @return {!Array<number>}
 */
proto.kwaipilot_llm_api.Embedding.prototype.getEmbeddingList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedFloatingPointField(this, 1));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.kwaipilot_llm_api.Embedding} returns this
 */
proto.kwaipilot_llm_api.Embedding.prototype.setEmbeddingList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_llm_api.Embedding} returns this
 */
proto.kwaipilot_llm_api.Embedding.prototype.addEmbedding = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_llm_api.Embedding} returns this
 */
proto.kwaipilot_llm_api.Embedding.prototype.clearEmbeddingList = function() {
  return this.setEmbeddingList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot_llm_api.EmbeddingResult.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot_llm_api.EmbeddingResult.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot_llm_api.EmbeddingResult} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.EmbeddingResult.toObject = function(includeInstance, msg) {
  var f, obj = {
    embeddingResultMap: (f = msg.getEmbeddingResultMap()) ? f.toObject(includeInstance, proto.kwaipilot_llm_api.Embedding.toObject) : []
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.EmbeddingResult}
 */
proto.kwaipilot_llm_api.EmbeddingResult.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.EmbeddingResult;
  return proto.kwaipilot_llm_api.EmbeddingResult.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.EmbeddingResult} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.EmbeddingResult}
 */
proto.kwaipilot_llm_api.EmbeddingResult.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = msg.getEmbeddingResultMap();
      reader.readMessage(value, function(message, reader) {
        jspb.Map.deserializeBinary(message, reader, jspb.BinaryReader.prototype.readString, jspb.BinaryReader.prototype.readMessage, proto.kwaipilot_llm_api.Embedding.deserializeBinaryFromReader, "", new proto.kwaipilot_llm_api.Embedding());
         });
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.EmbeddingResult.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.EmbeddingResult.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.EmbeddingResult} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.EmbeddingResult.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getEmbeddingResultMap(true);
  if (f && f.getLength() > 0) {
    f.serializeBinary(1, writer, jspb.BinaryWriter.prototype.writeString, jspb.BinaryWriter.prototype.writeMessage, proto.kwaipilot_llm_api.Embedding.serializeBinaryToWriter);
  }
};


/**
 * map<string, Embedding> embedding_result = 1;
 * @param {boolean=} opt_noLazyCreate Do not create the map if
 * empty, instead returning `undefined`
 * @return {!jspb.Map<string,!proto.kwaipilot_llm_api.Embedding>}
 */
proto.kwaipilot_llm_api.EmbeddingResult.prototype.getEmbeddingResultMap = function(opt_noLazyCreate) {
  return /** @type {!jspb.Map<string,!proto.kwaipilot_llm_api.Embedding>} */ (
      jspb.Message.getMapField(this, 1, opt_noLazyCreate,
      proto.kwaipilot_llm_api.Embedding));
};


/**
 * Clears values from the map. The map will be non-null.
 * @return {!proto.kwaipilot_llm_api.EmbeddingResult} returns this
 */
proto.kwaipilot_llm_api.EmbeddingResult.prototype.clearEmbeddingResultMap = function() {
  this.getEmbeddingResultMap().clear();
  return this;};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    embeddingResultsList: jspb.Message.toObjectList(msg.getEmbeddingResultsList(),
    proto.kwaipilot_llm_api.EmbeddingResult.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse;
  return proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.kwaipilot_llm_api.EmbeddingResult;
      reader.readMessage(value,proto.kwaipilot_llm_api.EmbeddingResult.deserializeBinaryFromReader);
      msg.addEmbeddingResults(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getEmbeddingResultsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.kwaipilot_llm_api.EmbeddingResult.serializeBinaryToWriter
    );
  }
};


/**
 * repeated EmbeddingResult embedding_results = 1;
 * @return {!Array<!proto.kwaipilot_llm_api.EmbeddingResult>}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.prototype.getEmbeddingResultsList = function() {
  return /** @type{!Array<!proto.kwaipilot_llm_api.EmbeddingResult>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot_llm_api.EmbeddingResult, 1));
};


/**
 * @param {!Array<!proto.kwaipilot_llm_api.EmbeddingResult>} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse} returns this
*/
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.prototype.setEmbeddingResultsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.kwaipilot_llm_api.EmbeddingResult=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_llm_api.EmbeddingResult}
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.prototype.addEmbeddingResults = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.kwaipilot_llm_api.EmbeddingResult, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse} returns this
 */
proto.kwaipilot_llm_api.KwaipilotEmbeddingResponse.prototype.clearEmbeddingResultsList = function() {
  return this.setEmbeddingResultsList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot_llm_api.KwaipilotRerankingRequest.repeatedFields_ = [2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot_llm_api.KwaipilotRerankingRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot_llm_api.KwaipilotRerankingRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot_llm_api.KwaipilotRerankingRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotRerankingRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    query: jspb.Message.getFieldWithDefault(msg, 1, ""),
    documentsList: (f = jspb.Message.getRepeatedField(msg, 2)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.KwaipilotRerankingRequest}
 */
proto.kwaipilot_llm_api.KwaipilotRerankingRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.KwaipilotRerankingRequest;
  return proto.kwaipilot_llm_api.KwaipilotRerankingRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.KwaipilotRerankingRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.KwaipilotRerankingRequest}
 */
proto.kwaipilot_llm_api.KwaipilotRerankingRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setQuery(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.addDocuments(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.KwaipilotRerankingRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.KwaipilotRerankingRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.KwaipilotRerankingRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotRerankingRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getQuery();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getDocumentsList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      2,
      f
    );
  }
};


/**
 * optional string query = 1;
 * @return {string}
 */
proto.kwaipilot_llm_api.KwaipilotRerankingRequest.prototype.getQuery = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotRerankingRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotRerankingRequest.prototype.setQuery = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * repeated string documents = 2;
 * @return {!Array<string>}
 */
proto.kwaipilot_llm_api.KwaipilotRerankingRequest.prototype.getDocumentsList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 2));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotRerankingRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotRerankingRequest.prototype.setDocumentsList = function(value) {
  return jspb.Message.setField(this, 2, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_llm_api.KwaipilotRerankingRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotRerankingRequest.prototype.addDocuments = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_llm_api.KwaipilotRerankingRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotRerankingRequest.prototype.clearDocumentsList = function() {
  return this.setDocumentsList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot_llm_api.KwaipilotRerankingResult.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot_llm_api.KwaipilotRerankingResult.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot_llm_api.KwaipilotRerankingResult.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot_llm_api.KwaipilotRerankingResult} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotRerankingResult.toObject = function(includeInstance, msg) {
  var f, obj = {
    scoresList: (f = jspb.Message.getRepeatedFloatingPointField(msg, 1)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.KwaipilotRerankingResult}
 */
proto.kwaipilot_llm_api.KwaipilotRerankingResult.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.KwaipilotRerankingResult;
  return proto.kwaipilot_llm_api.KwaipilotRerankingResult.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.KwaipilotRerankingResult} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.KwaipilotRerankingResult}
 */
proto.kwaipilot_llm_api.KwaipilotRerankingResult.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedFloat() : [reader.readFloat()]);
      for (var i = 0; i < values.length; i++) {
        msg.addScores(values[i]);
      }
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.KwaipilotRerankingResult.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.KwaipilotRerankingResult.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.KwaipilotRerankingResult} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotRerankingResult.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getScoresList();
  if (f.length > 0) {
    writer.writePackedFloat(
      1,
      f
    );
  }
};


/**
 * repeated float scores = 1;
 * @return {!Array<number>}
 */
proto.kwaipilot_llm_api.KwaipilotRerankingResult.prototype.getScoresList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedFloatingPointField(this, 1));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotRerankingResult} returns this
 */
proto.kwaipilot_llm_api.KwaipilotRerankingResult.prototype.setScoresList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_llm_api.KwaipilotRerankingResult} returns this
 */
proto.kwaipilot_llm_api.KwaipilotRerankingResult.prototype.addScores = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_llm_api.KwaipilotRerankingResult} returns this
 */
proto.kwaipilot_llm_api.KwaipilotRerankingResult.prototype.clearScoresList = function() {
  return this.setScoresList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    historysList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f,
    language: jspb.Message.getFieldWithDefault(msg, 2, ""),
    cursorOffset: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest}
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest;
  return proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest}
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.addHistorys(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setLanguage(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setCursorOffset(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getHistorysList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      1,
      f
    );
  }
  f = message.getLanguage();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getCursorOffset();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
};


/**
 * repeated string historys = 1;
 * @return {!Array<string>}
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.prototype.getHistorysList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.prototype.setHistorysList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.prototype.addHistorys = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.prototype.clearHistorysList = function() {
  return this.setHistorysList([]);
};


/**
 * optional string language = 2;
 * @return {string}
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.prototype.getLanguage = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.prototype.setLanguage = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 cursor_offset = 4;
 * @return {number}
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.prototype.getCursorOffset = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictRequest.prototype.setCursorOffset = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot_llm_api.CodeEdit.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot_llm_api.CodeEdit.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot_llm_api.CodeEdit} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.CodeEdit.toObject = function(includeInstance, msg) {
  var f, obj = {
    search: jspb.Message.getFieldWithDefault(msg, 1, ""),
    replace: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.CodeEdit}
 */
proto.kwaipilot_llm_api.CodeEdit.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.CodeEdit;
  return proto.kwaipilot_llm_api.CodeEdit.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.CodeEdit} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.CodeEdit}
 */
proto.kwaipilot_llm_api.CodeEdit.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSearch(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setReplace(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.CodeEdit.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.CodeEdit.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.CodeEdit} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.CodeEdit.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSearch();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getReplace();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string search = 1;
 * @return {string}
 */
proto.kwaipilot_llm_api.CodeEdit.prototype.getSearch = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot_llm_api.CodeEdit} returns this
 */
proto.kwaipilot_llm_api.CodeEdit.prototype.setSearch = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string replace = 2;
 * @return {string}
 */
proto.kwaipilot_llm_api.CodeEdit.prototype.getReplace = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot_llm_api.CodeEdit} returns this
 */
proto.kwaipilot_llm_api.CodeEdit.prototype.setReplace = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult.toObject = function(includeInstance, msg) {
  var f, obj = {
    searchReplacesList: jspb.Message.toObjectList(msg.getSearchReplacesList(),
    proto.kwaipilot_llm_api.CodeEdit.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult}
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult;
  return proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult}
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.kwaipilot_llm_api.CodeEdit;
      reader.readMessage(value,proto.kwaipilot_llm_api.CodeEdit.deserializeBinaryFromReader);
      msg.addSearchReplaces(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSearchReplacesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.kwaipilot_llm_api.CodeEdit.serializeBinaryToWriter
    );
  }
};


/**
 * repeated CodeEdit search_replaces = 1;
 * @return {!Array<!proto.kwaipilot_llm_api.CodeEdit>}
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult.prototype.getSearchReplacesList = function() {
  return /** @type{!Array<!proto.kwaipilot_llm_api.CodeEdit>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot_llm_api.CodeEdit, 1));
};


/**
 * @param {!Array<!proto.kwaipilot_llm_api.CodeEdit>} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult} returns this
*/
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult.prototype.setSearchReplacesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.kwaipilot_llm_api.CodeEdit=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_llm_api.CodeEdit}
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult.prototype.addSearchReplaces = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.kwaipilot_llm_api.CodeEdit, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult} returns this
 */
proto.kwaipilot_llm_api.KwaipilotCodeEditPredictResult.prototype.clearSearchReplacesList = function() {
  return this.setSearchReplacesList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot_llm_api.File.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot_llm_api.File.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot_llm_api.File} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.File.toObject = function(includeInstance, msg) {
  var f, obj = {
    filePath: jspb.Message.getFieldWithDefault(msg, 1, ""),
    fileContent: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.File}
 */
proto.kwaipilot_llm_api.File.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.File;
  return proto.kwaipilot_llm_api.File.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.File} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.File}
 */
proto.kwaipilot_llm_api.File.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setFilePath(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setFileContent(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.File.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.File.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.File} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.File.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFilePath();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getFileContent();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string file_path = 1;
 * @return {string}
 */
proto.kwaipilot_llm_api.File.prototype.getFilePath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot_llm_api.File} returns this
 */
proto.kwaipilot_llm_api.File.prototype.setFilePath = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string file_content = 2;
 * @return {string}
 */
proto.kwaipilot_llm_api.File.prototype.getFileContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot_llm_api.File} returns this
 */
proto.kwaipilot_llm_api.File.prototype.setFileContent = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    relatedFilesList: jspb.Message.toObjectList(msg.getRelatedFilesList(),
    proto.kwaipilot_llm_api.File.toObject, includeInstance),
    modelOutput: jspb.Message.getFieldWithDefault(msg, 2, ""),
    instruction: jspb.Message.getFieldWithDefault(msg, 3, ""),
    language: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest}
 */
proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest;
  return proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest}
 */
proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.kwaipilot_llm_api.File;
      reader.readMessage(value,proto.kwaipilot_llm_api.File.deserializeBinaryFromReader);
      msg.addRelatedFiles(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setModelOutput(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setInstruction(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setLanguage(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRelatedFilesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.kwaipilot_llm_api.File.serializeBinaryToWriter
    );
  }
  f = message.getModelOutput();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getInstruction();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getLanguage();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * repeated File related_files = 1;
 * @return {!Array<!proto.kwaipilot_llm_api.File>}
 */
proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.prototype.getRelatedFilesList = function() {
  return /** @type{!Array<!proto.kwaipilot_llm_api.File>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot_llm_api.File, 1));
};


/**
 * @param {!Array<!proto.kwaipilot_llm_api.File>} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest} returns this
*/
proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.prototype.setRelatedFilesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.kwaipilot_llm_api.File=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot_llm_api.File}
 */
proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.prototype.addRelatedFiles = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.kwaipilot_llm_api.File, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.prototype.clearRelatedFilesList = function() {
  return this.setRelatedFilesList([]);
};


/**
 * optional string model_output = 2;
 * @return {string}
 */
proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.prototype.getModelOutput = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.prototype.setModelOutput = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string instruction = 3;
 * @return {string}
 */
proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.prototype.getInstruction = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.prototype.setInstruction = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string language = 4;
 * @return {string}
 */
proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.prototype.getLanguage = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotInstantApplyRequest.prototype.setLanguage = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


goog.object.extend(exports, proto.kwaipilot_llm_api);
