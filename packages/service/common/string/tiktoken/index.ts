import {
  ChatCompletionContentPart,
  ChatCompletionCreateParams,
  ChatCompletionMessageParam,
  ChatCompletionTool
} from '@fastgpt/global/core/ai/type';
import { chats2GPTMessages } from '@fastgpt/global/core/chat/adapt';
import { ChatItemType } from '@fastgpt/global/core/chat/type';
import { WorkerNameEnum, getWorker } from '../../../worker/utils';
import { ChatCompletionRequestMessageRoleEnum } from '@fastgpt/global/core/ai/constants';
import { getNanoid } from '@fastgpt/global/common/string/tools';
import { createLogger, perLogger } from '@fastgpt/global/common/util/logger';
import { getGatewayAppLLMCluster, getKwaipilotRetryConfig } from '../../kconf';
import { GatewayAppInfo } from '@fastgpt/global/core/workflow/type';
import {
  getLLMTokenizerConsumer,
  KwaipilotLlmRequest
} from '../../rpc/llmKwaipilotTokenizer/index';

const logger = createLogger('tiktoken');

export const getTiktokenWorker = () => {
  if (global.tiktokenWorker) {
    return global.tiktokenWorker;
  }

  const worker = getWorker(WorkerNameEnum.countGptMessagesTokens);

  worker.on('message', ({ id, data }: { id: string; data: number }) => {
    const callback = global.tiktokenWorker?.callbackMap?.[id];

    if (callback) {
      callback?.(data);
      delete global.tiktokenWorker.callbackMap[id];
    }
  });

  global.tiktokenWorker = {
    worker,
    callbackMap: {}
  };

  return global.tiktokenWorker;
};

export const countGptMessagesTokens = (
  messages: ChatCompletionMessageParam[],
  tools?: ChatCompletionTool[],
  functionCall?: ChatCompletionCreateParams.Function[]
) => {
  return new Promise<number>((resolve) => {
    const start = Date.now();

    const { worker, callbackMap } = getTiktokenWorker();
    const id = getNanoid();

    const timer = setTimeout(() => {
      resolve(0);
      perLogger.perf({
        subtag: 'count-tokens-timeout'
      });
      logger.info(`count token timeout, id: ${id}`);
      delete callbackMap[id];
    }, 1000);

    callbackMap[id] = (data) => {
      resolve(data);
      clearTimeout(timer);

      // 检测是否有内存泄漏
      logger.info(`count token time: ${Date.now() - start}, token: ${data}`);
      console.log(Object.keys(global.tiktokenWorker.callbackMap));
    };

    worker.postMessage({
      id,
      messages,
      tools,
      functionCall
    });
  });
};

export const countMessagesTokens = (messages: ChatItemType[], supportImg = false) => {
  const adaptMessages = chats2GPTMessages({ messages, reserveId: true, supportImg });

  return countGptMessagesTokens(adaptMessages);
};

/* count one prompt tokens */
export const countPromptTokens = async (
  prompt: string | ChatCompletionContentPart[] | null | undefined = '',
  role: '' | `${ChatCompletionRequestMessageRoleEnum}` = ''
) => {
  const total = await countGptMessagesTokens([
    {
      //@ts-ignore
      role,
      content: prompt
    }
  ]);

  return total;
};

// Kwaipilot 和 Deepseek 走后端接口，其他走 worker 计算
export const countTokensByModel = async (
  model: string,
  data: {
    messages: ChatCompletionMessageParam[];
    tools?: ChatCompletionTool[];
    functionCall?: ChatCompletionCreateParams.Function[];
  }
) => {
  if (model.startsWith('kwaipilot') || model.startsWith('deepseek')) {
    try {
      const consumer = await getLLMTokenizerConsumer();
      const r = new KwaipilotLlmRequest();

      const bodyJsonStr = JSON.stringify({
        rpc_name: model,
        text: JSON.stringify(data.messages)
      });
      r.setRequestBodyJsonStr(bodyJsonStr);
      logger.info(`countTokensByModel bodyJsonStr: ${bodyJsonStr}`);
      const service = await consumer.getPromisifyService('KwaipilotLlmApiService');

      const res = await service.tokenizerEncode(r);
      const resJson = JSON.parse(res.getRequestBodyJsonStr());
      const tokenIds = resJson.token_ids;
      logger.info(`countTokensByModel res: ${tokenIds.length}`);
      return tokenIds.length;
    } catch (e) {
      logger.error(`countTokensByModel error ${e}`, { error: e });
      perLogger.perf({
        subtag: 'count-tokens-error-api',
        extra1: model
      });
      // 如果后端接口报错，则使用本地计算
      return await countGptMessagesTokens(data.messages, data.tools, data.functionCall);
    }
  }

  return await countGptMessagesTokens(data.messages, data.tools, data.functionCall);
};
