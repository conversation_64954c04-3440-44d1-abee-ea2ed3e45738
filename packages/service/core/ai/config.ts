import OpenAI from '@fastgpt/global/core/ai';
import { ProxyAgent, fetch as undiciFetch } from 'undici';
import { getOpenAIConfig } from '../../common/kconf';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { getOneProxy, ProxyRegion } from '../../common/proxy';
import { isIP } from 'net';
import { installDNSCache } from '@fastgpt/global/common/dns';

const logger = createLogger('core-ai-config');

async function getProxyAgent(region: ProxyRegion) {
  const proxy = await getOneProxy(region);
  if (proxy) {
    let host = proxy.host;
    if (!isIP(host) && host !== 'localhost') {
      logger.info(`dns lookup ${host}`);
      const cacheable = installDNSCache();
      const res = await cacheable.lookupAsync(host);
      logger.info(`dns lookup ${host} result ${res.address}`);
      host = res.address;
    }

    return new ProxyAgent(`http://${host}:${proxy.port}`);
  }
}

export const openaiBaseUrl = process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1';

// 非流式请求超时时间, 单位毫秒, 默认60秒
export const NON_STREAMING_TIMEOUT = 300 * 1000;

// 通过范型，获取构造函数里的参数类型
type OpenAIConfig = Exclude<ConstructorParameters<typeof OpenAI>[0], undefined>;

export const getAIApi = async (props: { model: string; timeout?: number }) => {
  const { model, timeout } = props || {};
  const provider = model.startsWith('gpt-4o') ? 'azure' : 'other';
  const configList = await getOpenAIConfig(provider);

  // 从 config 中随机取一个
  const config = configList[Math.floor(Math.random() * configList.length)];
  logger.info(`use api config ${JSON.stringify(config)}`);

  const openAIConfig: OpenAIConfig = {
    baseURL: config.baseUrl,
    apiKey: config.apiKey,
    httpAgent: global.httpsAgent,
    timeout,
    maxRetries: 2,
    defaultQuery: config.queries,
    defaultHeaders: config.headers,
    fetch: (async (url: string, options: any) => {
      // clouddev 或者 kws 服务使用代理
      const dispatcher = await getProxyAgent(config.proxy);
      if (dispatcher) {
        (options as any).dispatcher = dispatcher;
      }
      return undiciFetch(url, options) as any;
    }) as any
  };

  // 排除 fetch，打印
  logger.info(`openai config ${JSON.stringify({ ...openAIConfig, fetch: undefined })}`);
  return { ai: new OpenAI(openAIConfig), config: openAIConfig };
};
