import { IS_KWS_PREONLINE, IS_KWS_PROD, KWS_LANE_ID } from '@fastgpt/global/core/chat/constants';

// 线上环境或者非泳道预发环境，使用线上 rag 服务
const USE_PROD_RAG = IS_KWS_PROD || (IS_KWS_PREONLINE && !KWS_LANE_ID);

export const BASE_URL = USE_PROD_RAG
  ? 'http://kwaipilot-server.internal/rag'
  : 'https://infra-node.corp.kuaishou.com/rag';

export const RAG_URL = {
  WEB: `${BASE_URL}/web_search_stream`,
  CODE: `${BASE_URL}/code_search_stream`,
  DOCS: `${BASE_URL}/doc_search_stream`
} as const;

export const STAGE_FROM_API = {
  INTENT: 'intent',
  QUERY: 'query',
  SEARCH: 'search',
  CRAWL: 'crawl',
  RESULT: 'result',
  STANDALONE_QUERY: 'standalone-query'
} as const;

export const STAGE = {
  INTENT: 'INTENT',
  QUERY: 'QUERY',
  SEARCH: 'SEARCH',
  CRAWL: 'CRAWL',
  RESULT: 'RESULT',
  STANDALONE_QUERY: 'STANDALONE_QUERY',
  UNKNOWN: 'UNKNOWN'
} as const;

export const INTENT = {
  SEARCH: 'search',
  SUMMARY: 'summary',
  CHAT: 'chat'
} as const;
