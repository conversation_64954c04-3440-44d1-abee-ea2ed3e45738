import {
  DispatchNodeResponseKeyEnum,
  SseResponseEventEnum
} from '@fastgpt/global/core/workflow/runtime/constants';
import { responseWrite } from '../../../../../common/response';
import {
  collectRagDurationLog,
  coreServicePerf,
  CoreServiceSubTag,
  createLogger,
  type RagDurationLogMode,
  type RagDurationLogStage
} from '@fastgpt/global/common/util/logger';
import { getRemoteLLMModel, ModelTypeEnum } from '../../../../ai/model';
import { readFromEventSource } from '@fastgpt/global/common/util/fetch-event-source';
import { StreamRagProps, StreamRagResponse } from '../../type';
import { emitQuoteSource, getHistories } from '../../utils';
import { CodeBlock, Doc, WebContent } from './types';
import { getOfflineCaptureEmptyDataPrompt, getQuoteUserPromptFromKconf } from '../../../utils';
import { QuoteType } from '../../../../../common/kconf';
import { replaceVariable } from '../../httpUtils';
import { NodeOutputKeyEnum } from '@fastgpt/global/core/workflow/constants';
import {
  formatCodeRagResults,
  formatDocsRagResults,
  formatFileRagResults,
  formatWebRagResults,
  getFilesFromInput,
  filterRagResultsByMaxInput,
  getRagChatHistories
} from './utils';
import { RAG_URL, STAGE_FROM_API, STAGE, INTENT } from './const';
import { type ValueOf } from 'next/dist/shared/lib/constants';
import { countTokensByModel } from '../../../../../common/string/tiktoken';
import { KError } from '@fastgpt/global/common/error/kwaipilotError';

const logger = createLogger('node-agent-stream-rag');

// 技术文档：https://docs.corp.kuaishou.com/d/home/<USER>
export const dispatchStreamRag = async (props: StreamRagProps): Promise<StreamRagResponse> => {
  let {
    appId,
    gatewayAppInfo,
    emitter,
    user,
    histories: historiesOriginal,
    node: { name },
    traceId,
    inputFiles = [],
    conversationId,
    params: { streamRagMode, streamRagParams, userChatInput, systemPrompt },
    model
  } = props;

  if (!userChatInput) {
    return Promise.reject('没有搜索输入，对话无法正常运行');
  }

  if (!streamRagMode) {
    throw new Error('没有指定搜索模式，RAG 调用无法正常运行');
  }

  const URL_MATCH_REGEX =
    /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,63}\b([-a-zA-Z0-9()'@:%_\+.~#?!&//=]*)/gi;
  const isCodeMode = streamRagMode === 'code';
  const isDocsMode = streamRagMode === 'docs';
  const isWebMode = streamRagMode === 'web';
  const isCommonMode = streamRagMode === 'common';

  const questionHasUrl = URL_MATCH_REGEX.test(userChatInput);
  let userInputRewritten = '';

  const send = (data: { stage: ValueOf<typeof STAGE>; payload: unknown }) =>
    responseWrite({
      write: (t) => emitter.emit('data', t),
      event: SseResponseEventEnum.cotStage,
      data: JSON.stringify(data)
    });
  let intent = '';
  let results: unknown[] = [];
  let repoName = '';
  let commitId = '';
  let ragUrl: ValueOf<typeof RAG_URL> = RAG_URL.WEB;
  let quoteType: QuoteType = 'simple';
  let quoteChatInput = userChatInput;
  const histories = getHistories(6, historiesOriginal);
  const { ragFiles, availableInputFiles } = getFilesFromInput(inputFiles, histories);
  const shouldIgnoreRag = isCommonMode && !questionHasUrl && ragFiles.length === 0;
  const params = JSON.parse(streamRagParams);
  const modelItem = await getRemoteLLMModel(model);
  logger.info(`streamRag params: ${JSON.stringify(params)}`, {
    ...params,
    ...modelItem
  });

  // 辅助函数处理重复返回逻辑，现在位于 dispatchStreamRag 内部，可以复用 send 方法
  const handleNormalChatIntent = (userChatInput: string, results: unknown[]) => {
    send({
      stage: STAGE.INTENT,
      payload: {
        intent: INTENT.CHAT
      }
    });

    logger.info(`streamRag normal chat intent: ${INTENT.CHAT}`);

    return {
      [NodeOutputKeyEnum.streamRagMode]: streamRagMode,
      [NodeOutputKeyEnum.streamRagIntent]: 'chat',
      [NodeOutputKeyEnum.streamRagResult]: results,
      [NodeOutputKeyEnum.streamRagQuoteChatInput]: userChatInput,
      [DispatchNodeResponseKeyEnum.nodeResponse]: {
        streamRagMode,
        streamRagIntent: 'chat',
        streamRagResult: results,
        streamRagQuoteChatInput: userChatInput
      }
    };
  };
  // 普通模式，文件不存在，直接走 AI 问答
  if (shouldIgnoreRag) {
    return handleNormalChatIntent(userChatInput, results);
  }

  let ragMode: RagDurationLogMode = 'web';
  let ragSubtag: CoreServiceSubTag = 'rag-http-web';
  // 网页搜索模式
  if (isWebMode) {
    quoteType = 'web';
    ragMode = 'web';
    ragUrl = RAG_URL.WEB;
    ragSubtag = 'rag-http-web';
  }
  // 仓库问答模式
  else if (isCodeMode) {
    quoteType = 'code';
    ragMode = 'code';
    ragUrl = RAG_URL.CODE;
    repoName = params.repo_name;
    commitId = params.commit_id;
    ragSubtag = 'rag-http-code';
  }
  // 知识库模式
  else if (isDocsMode) {
    quoteType = 'docs';
    ragMode = 'kn';
    ragUrl = RAG_URL.DOCS;
    ragSubtag = 'rag-http-knowledge';
  }
  // 非联网模式并且用户问题中有url
  else if (isCommonMode && questionHasUrl && ragFiles.length === 0) {
    quoteType = 'web';
    ragMode = 'web';
    ragUrl = RAG_URL.WEB;
    ragSubtag = 'rag-http-web';
  }
  // 普通模式
  else {
    // 文件也是走 web search rag
    quoteType = 'file';
    ragMode = 'file';
    ragUrl = RAG_URL.WEB;
    ragSubtag = 'rag-http-web';
  }

  logger.info(`streamRag url: ${ragUrl}`);

  const chatHistories = getRagChatHistories(histories);
  logger.info(`streamRag chat history: ${JSON.stringify(chatHistories)}`);

  const reportCost = (stage: RagDurationLogStage) => {
    const now = +new Date();
    collectRagDurationLog({
      stage,
      mode: ragMode,
      beginTimestamp,
      duration: now - beginTimestamp,
      sessionId: conversationId,
      username: user.username,
      intent
    });
    beginTimestamp = now;
  };

  let beginTimestamp = +new Date();
  const startTimestamp = beginTimestamp;
  try {
    const uri = new URL(ragUrl);
    const formattedUrl = uri.toString();
    const requestBody = JSON.stringify({
      query: userChatInput,
      chat_history: chatHistories,
      uploaded_files: ragFiles.length > 0 ? ragFiles : undefined,
      withUrl: isCommonMode && ragMode === 'web' ? true : false,
      ...params
    });
    const abortController = new AbortController();
    const stream = readFromEventSource(formattedUrl, {
      method: 'POST',
      signal: abortController.signal,
      headers: {
        'Content-Type': 'application/json',
        'x-workflow-trace-id': traceId
      },
      body: requestBody,
      parseData: (data, event) => ({
        data,
        event
      })
    });

    logger.info(`streamRag stream post params: ${requestBody}`);

    // 代码库和知识库搜索，需要补充意图数据
    // https://docs.corp.kuaishou.com/d/home/<USER>
    if (isCodeMode || isDocsMode) {
      send({
        stage: STAGE.INTENT,
        payload: {
          intent: INTENT.SEARCH
        }
      });
    }

    for await (const chunk of stream) {
      if (!chunk.data) {
        continue;
      }

      const { data, event } = chunk;
      logger.info(`streamRag event: ${event}, data: ${data}`);
      let stage: ValueOf<typeof STAGE> = STAGE.UNKNOWN;
      let payload = JSON.parse(data) as Record<string, any>;

      // 意图识别阶段
      if (event === STAGE_FROM_API.INTENT) {
        stage = STAGE.INTENT;
        intent = payload.intent;
        reportCost('workflow-rag-intention');
        // 普通模式，不带文件时候，走到这里都是疑似需要联网（问题中带url）
        // 此时只支持意图是 SUMMARY 的情况（crawl），如果不是则将意图设置为 CHAT
        if (isCommonMode && ragFiles.length === 0 && intent !== INTENT.SUMMARY) {
          abortController.abort();
          return handleNormalChatIntent(userChatInput, results);
        }
      }
      // 原始 query 改写
      else if (event === STAGE_FROM_API.STANDALONE_QUERY) {
        stage = STAGE.STANDALONE_QUERY;
        userInputRewritten = payload.query;
        reportCost('workflow-rag-standalone-query');
      }
      // query 改写阶段
      else if (event === STAGE_FROM_API.QUERY) {
        stage = STAGE.QUERY;
        reportCost('workflow-rag-query');
      }
      // 网络搜索阶段
      else if (event === STAGE_FROM_API.SEARCH) {
        stage = STAGE.SEARCH;
        reportCost('workflow-rag-search');
      }
      // 网页抓取阶段
      else if (event === STAGE_FROM_API.CRAWL) {
        stage = STAGE.CRAWL;
        reportCost('workflow-rag-crawl');
      }
      // 结果返回阶段
      else if (event === STAGE_FROM_API.RESULT) {
        stage = STAGE.RESULT;
        results = payload.results;
        reportCost('workflow-rag-result');
      }

      // results 需要处理后，才会发送
      if (stage !== STAGE.RESULT) {
        send({ stage, payload });
      }
    }

    collectRagDurationLog({
      stage: 'workflow-rag-total',
      mode: ragMode,
      beginTimestamp: startTimestamp,
      duration: +new Date() - startTimestamp,
      sessionId: conversationId,
      username: user.username,
      intent
    });

    coreServicePerf({
      subtag: ragSubtag,
      startTime: startTimestamp,
      ok: true
    });
    // 普通模式下，如果是总结模式，则需要补充一条消息，将文件信息返回成 results
    if (ragMode === 'file' && intent === INTENT.SUMMARY) {
      const summaryResults: WebContent[] = availableInputFiles.map((file) => ({
        page_num: file.pages.length,
        content: file.content,
        prev_content: '',
        next_content: '',
        favicon: '',
        title: file.name,
        link: file.url,
        date: '',
        file_index: file.index
      }));

      results = summaryResults;
    }
    logger.info(`streamRag results: ${JSON.stringify(results)}`, results);
    const formatRagResults = (oriResults: unknown[]) => {
      const { quote, ragResults } =
        oriResults.length === 0
          ? { quote: '', ragResults: [] }
          : ragMode === 'file'
            ? formatFileRagResults(oriResults as WebContent[], availableInputFiles)
            : ragMode === 'web'
              ? formatWebRagResults(oriResults as WebContent[])
              : ragMode === 'code'
                ? formatCodeRagResults(oriResults as CodeBlock[], repoName, commitId)
                : formatDocsRagResults(oriResults as Doc[]);
      return { quote, ragResults };
    };

    const formatQuoteInput = async (oriQuote: string) => {
      let advancedUserPrompt = '';
      if (isCommonMode && ragMode === 'web' && !oriQuote) {
        advancedUserPrompt = await getOfflineCaptureEmptyDataPrompt();
      } else {
        advancedUserPrompt = await getQuoteUserPromptFromKconf(quoteType, model);
      }

      return replaceVariable(advancedUserPrompt, {
        quote: oriQuote,
        // 只有在 rag web 模式下，才会使用改写后的用户输入
        question: ragMode === 'web' && userInputRewritten ? userInputRewritten : userChatInput,
        repoName,
        date: new Date().toISOString()
      });
    };

    const countTokens = (text: string) => {
      return countTokensByModel(model || '', {
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: text }
        ]
      });
    };
    logger.info(`streamRag origin results: ${results.length}`, results);
    /** 开始处理Rag 结果截取 */
    const filteredResults = await filterRagResultsByMaxInput({
      originResults: results,
      // 留出 300 的 buffer
      maxTokens: modelItem.maxContext - 300,
      countTokens,
      formatRagResults,
      formatQuoteInput
    });
    const { ragResults } = filteredResults;
    if (filteredResults.quoteChatInput) {
      quoteChatInput = filteredResults.quoteChatInput;
    }
    logger.info(`streamRag filtered results: ${ragResults.length}`, ragResults);
    send({
      stage: STAGE.RESULT,
      payload: {
        results: ragResults
      }
    });
    emitQuoteSource(emitter, ragResults);

    return {
      [NodeOutputKeyEnum.streamRagMode]: streamRagMode,
      [NodeOutputKeyEnum.streamRagIntent]: intent,
      [NodeOutputKeyEnum.streamRagResult]: results,
      [NodeOutputKeyEnum.streamRagQuoteChatInput]: quoteChatInput,
      [DispatchNodeResponseKeyEnum.nodeResponse]: {
        streamRagMode,
        streamRagIntent: intent,
        streamRagResult: results,
        streamRagQuoteChatInput: quoteChatInput
      }
    };
  } catch (err: unknown) {
    logger.error(`streamRag error: ${err}`);
    coreServicePerf({
      subtag: ragSubtag,
      startTime: startTimestamp,
      ok: false
    });
    throw new KError('RAG_REQUEST_ERROR', 'modelError', {
      error: err
    });
  }
};
