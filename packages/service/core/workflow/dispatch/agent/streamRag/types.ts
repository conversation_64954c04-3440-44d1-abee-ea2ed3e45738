import { QuoteSource } from '../../type';

export interface WebContent {
  title: string;
  link: string;
  date: string;
  favicon: string;
  content: string;
  prev_content: string;
  next_content: string;
  file_index: number;
  page_num: number;
}

export interface CodeBlock {
  code_content: string;
  score: number;
  metadata: string;
}

export interface Doc {
  type: string;
  content: string;
  link: string;
  title: string;
  knowledge_repo_id: string;
  knowledge_repo_name: string;
}

export type FormattedRagResults = {
  quote: string;
  ragResults: QuoteSource[];
};
