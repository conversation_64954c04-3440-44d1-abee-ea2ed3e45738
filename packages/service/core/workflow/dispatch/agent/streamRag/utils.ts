import {
  ChatFileTypeEnum,
  ChatItemValueTypeEnum,
  ChatRoleEnum
} from '@fastgpt/global/core/chat/constants';
import { type ChatItemType, type UserChatFile } from '@fastgpt/global/core/chat/type';
import { Code<PERSON>lock, Doc, FormattedRagResults, WebContent } from './types';
import { createLogger } from '@fastgpt/global/common/util/logger';
import {
  DocQuoteSource,
  FileQuoteSource,
  QuoteSource,
  RepoQuoteSource,
  WebQuoteSource
} from '../../type';

const logger = createLogger('stream-rag-utils');

type RagSearchFile = {
  filename: string;
  file_index: number;
  page_num: number;
  page_content: string;
};

type RagChatHistory = {
  role: 'user' | 'assistant';
  content: string;
};

export function getFilesFromInput(
  inputFiles: UserChatFile[],
  histories: ChatItemType[]
): {
  ragFiles: RagSearchFile[];
  availableInputFiles: UserChatFile[];
} {
  const availableInputFiles: UserChatFile[] = [];
  const ragFiles: RagSearchFile[] = [];
  inputFiles.forEach((file) => {
    if (file.type === ChatFileTypeEnum.file && file.content) {
      availableInputFiles.push(file);
    }
  });
  histories.forEach((item) => {
    if (item.obj === ChatRoleEnum.Human) {
      item.value.forEach((v) => {
        if (v.type === ChatItemValueTypeEnum.file && v.file && v.file.content) {
          availableInputFiles.push(v.file);
        }
      });
    }
  });

  availableInputFiles.forEach((file) => {
    // 如果没有分页信息，则将整个文件作为一个分页
    if (file.pages.length === 0 && file.content) {
      ragFiles.push({
        filename: file.name,
        file_index: file.index,
        page_num: 1,
        page_content: file.content
      });
      return;
    }

    // 如果有分页信息，则将每个分页作为一个 RagSearchFile
    file.pages.forEach((page) => {
      if (page.content) {
        ragFiles.push({
          filename: file.name,
          file_index: file.index,
          page_num: page.pageNumber,
          page_content: page.content
        });
      }
    });
  });

  return {
    ragFiles,
    availableInputFiles
  };
}

export function getRagChatHistories(histories: ChatItemType[]): RagChatHistory[] {
  const chatHistory: RagChatHistory[] = [];

  histories.forEach((h) => {
    if (h.obj === ChatRoleEnum.System) {
      return;
    }

    const content = h.value
      .map((v) => (v.type === ChatItemValueTypeEnum.text ? v.text?.content : ''))
      .filter(Boolean)
      .join('\n');

    chatHistory.push({
      role: h.obj === ChatRoleEnum.AI ? 'assistant' : 'user',
      content
    });
  });

  return chatHistory;
}

export function formatFileRagResults(
  results: WebContent[],
  availableInputFiles: UserChatFile[]
): FormattedRagResults {
  const quote = results
    .map(
      (r, idx) =>
        `### 文档片段 [${idx + 1}]:\n(文件名:《${r.title}》, 第${r.file_index}个文件,第${r.page_num}页): ${r.content}`
    )
    .join('\n<hr>\n\n');

  const ragResults: FileQuoteSource[] = [];
  results.forEach((it) => {
    const file = availableInputFiles.find((i) => i.index === it.file_index);
    ragResults.push({
      pageNum: it.page_num,
      content: it.content,
      filename: file?.name || '',
      url: file?.url || '',
      type: 'file'
    });
  });

  return {
    quote,
    ragResults
  };
}

export function formatWebRagResults(results: WebContent[]): FormattedRagResults {
  const quote = results
    .map(
      (r, idx) =>
        `### 文档片段 [${idx + 1}]:\n(标题:《${r.title}》,发布日期：${r.date}): ${r.content}`
    )
    .join('\n<hr>\n\n');
  const ragResults: WebQuoteSource[] = results.map((r) => ({
    title: r.title,
    link: r.link,
    date: r.date,
    favicon: r.favicon,
    content: r.content,
    prevContent: r.prev_content,
    nextContent: r.next_content,
    type: 'web'
  }));

  return {
    quote,
    ragResults
  };
}

export function formatDocsRagResults(results: Doc[]): FormattedRagResults {
  const quote = results
    .map((r, idx) => `### 文档片段 [${idx + 1}]:\n(文件名:《 ${r.title}》: ${r.content}`)
    .join('\n<hr>\n\n');
  const ragResults: DocQuoteSource[] = results.map((r) => ({
    title: r.title,
    type: 'doc',
    content: r.content,
    link: r.link,
    knowledgeRepoId: r.knowledge_repo_id,
    knowledgeRepoName: r.knowledge_repo_name
  }));

  return {
    quote,
    ragResults
  };
}

export function formatCodeRagResults(
  results: CodeBlock[],
  repoName: string,
  commitId: string
): FormattedRagResults {
  const ragResults: (RepoQuoteSource & {
    codeType: string;
    functionName: string;
    functionSignature: string;
  })[] = results.map((r) => {
    const metadata = JSON.parse(r.metadata) as {
      line_from: number;
      line_to: number;
      language: string;
      file_path: string;
      signature: string;
      code_type: string;
      name: string;
    };

    const filename = metadata.file_path.split('/').pop() || '';
    return {
      type: 'repo',
      code: r.code_content,
      startLineNo: metadata.line_from,
      endLineNo: metadata.line_to,
      language: metadata.language,
      path: metadata.file_path,
      repoName: repoName,
      fileName: filename,
      commitId: commitId,
      functionName: metadata.name,
      functionSignature: metadata.signature,
      codeType: metadata.code_type
    };
  });

  const quote = ragResults
    .map(
      (r, idx) =>
        `### 相关代码片段 [${idx + 1}]:\n#### 代码片段基本信息\n- 文件路径: ${r.path}\n- 类型: ${r.codeType}\n#### 代码片段内容\n- 名称: ${r.functionName}\n- 签名: ${r.functionSignature}\n- 内容:\n\`\`\`${r.language}\n${r.code}\n\`\`\``
    )
    .join('\n');

  return {
    quote,
    ragResults
  };
}

// 根据最大输入限制过滤rag结果
export const filterRagResultsByMaxInput = async (data: {
  originResults: unknown[];
  maxTokens: number;
  countTokens: (text: string) => Promise<number>;
  formatRagResults: (results: unknown[]) => FormattedRagResults;
  formatQuoteInput: (quote: string) => Promise<string>;
}): Promise<{ quoteChatInput: string; ragResults: QuoteSource[] }> => {
  const { originResults, maxTokens, countTokens, formatRagResults, formatQuoteInput } = data;
  if (originResults.length === 0) {
    return { quoteChatInput: '', ragResults: [] };
  }

  // 循环执行，直到 ragResults 的tokens 小于maxTokens
  let quoteChatInput = '';
  let ragResults: QuoteSource[] = [];
  let results = originResults;
  while (true) {
    const result = formatRagResults(results);
    ragResults = result.ragResults;

    quoteChatInput = await formatQuoteInput(result.quote);
    // 如果字数少于 max_tokens 的一半，则不进行计算，避免耗时过长
    if (quoteChatInput.length < maxTokens * 0.5) {
      break;
    }
    const tokens = await countTokens(quoteChatInput);
    logger.info(
      `current tokens: ${tokens}, maxTokens: ${maxTokens}, quoteChatInput: ${quoteChatInput}`
    );
    // 如果 tokens 小于 maxTokens 或者 results 为 1（至少要保留一个），则停止循环
    if (tokens <= maxTokens || results.length <= 1) {
      break;
    } else {
      results.pop();
    }
  }
  return { quoteChatInput, ragResults };
};
