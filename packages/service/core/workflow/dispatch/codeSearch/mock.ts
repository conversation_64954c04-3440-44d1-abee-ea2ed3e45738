export const MOCK_DATA = {
  status: 200,
  list: [
    {
      id: '454271813878077060',
      code: 'tmp_download/\n    ├── src/\n        ├── CanvasLayer.js\n    ├── examples/\n    ├── README.md\n',
      repoName: 'huiyan-fe/CanvasLayer',
      startLineNo: 0,
      endLineNo: 6,
      startColNo: 0,
      endColNo: 6,
      language: 'txt',
      path: '项目根目录结构.txt',
      fileName: '项目根目录结构.txt',
      commitId: 'bc0adec0c0ee3daeb7e9f8303185791ba6567077',
      functionName: '',
      functionSignature: '',
      codeType: ''
    },
    {
      id: '454271813878077000',
      code: 'function getCurveByTwoPoints(obj1, obj2, count) {\n    if (!obj1 || !obj2 || !(obj1 instanceof BMap.Point) || !(obj2 instanceof BMap.Point)) {\n      return null;\n    }\n\n    var B1 = function(x) {\n      return 1 - 2 * x + x * x;\n    };\n    var B2 = function(x) {\n      return 2 * x - 2 * x * x;\n    };\n    var B3 = function(x) {\n      return x * x;\n    };\n\n    curveCoordinates = [];\n\n    count= count || 30; // 曲线是由一些小的线段组成的，这个表示这个曲线所有到的折线的个数\n    var isFuture=false;\n    var t, h, h2, lat3, lng3, j, t2;\n    var LnArray = [];\n    var i = 0;\n    var inc = 0;\n\n    if (typeof(obj2) == "undefined") {\n      if (typeof(curveCoordinates) != "undefined") {\n        curveCoordinates = [];\n      }\n      return;\n    }\n\n    var lat1 = parseFloat(obj1.lat);\n    var lat2 = parseFloat(obj2.lat);\n    var lng1 = parseFloat(obj1.lng);\n    var lng2 = parseFloat(obj2.lng);\n      \n    // 计算曲线角度的方法\n    /*\n    if (lng2 > lng1) {\n      if (parseFloat(lng2-lng1) > 180) {\n        if (lng1 < 0) {\n          lng1 = parseFloat(180 + 180 + lng1);\n        }\n      }\n    }\n    \n    if (lng1 > lng2) {\n      if (parseFloat(lng1-lng2) > 180) {\n        if (lng2 < 0) {\n          lng2 = parseFloat(180 + 180 + lng2);\n        }\n      }\n    }\n    */\n    j = 0;\n    t2 = 0;\n    if (lat2 == lat1) {\n      t = 0;\n      h = lng1 - lng2;\n    } else if (lng2 == lng1) {\n      t = Math.PI / 2;\n      h = lat1 - lat2;\n    } else {\n      t = Math.atan((lat2 - lat1) / (lng2 - lng1));\n      h = (lat2 - lat1) / Math.sin(t);\n    }\n    if (t2 == 0) {\n      t2 = (t + (Math.PI / 5));\n    }\n    h2 = h / 2;\n    lng3 = h2 * Math.cos(t2) + lng1;\n    lat3 = h2 * Math.sin(t2) + lat1;\n\n    for (i = 0; i < count + 1; i++) {\n      curveCoordinates.push(new BMap.Point(\n        (lng1 * B1(inc) + lng3 * B2(inc)) + lng2 * B3(inc),\n        (lat1 * B1(inc) + lat3 * B2(inc) + lat2 * B3(inc))\n      ));\n      inc = inc + (1 / count);\n    }\n    return curveCoordinates;\n  }',
      repoName: 'huiyan-fe/CanvasLayer',
      startLineNo: 109,
      endLineNo: 195,
      startColNo: 109,
      endColNo: 195,
      language: 'javascript',
      path: 'examples/animationline.js',
      fileName: 'animationline.js',
      commitId: 'bc0adec0c0ee3daeb7e9f8303185791ba6567077',
      functionName: '',
      functionSignature: '',
      codeType: ''
    },
    {
      id: '454271813878077060',
      code: "function CanvasLayer(options) {\n    this.options = options || {};\n    this.paneName = this.options.paneName || 'labelPane';\n    this.zIndex = this.options.zIndex || 0;\n    this._map = options.map;\n    this._lastDrawTime = null;\n    this.show();\n}",
      repoName: 'huiyan-fe/CanvasLayer',
      startLineNo: 1,
      endLineNo: 19,
      startColNo: 1,
      endColNo: 19,
      language: 'javascript',
      path: 'src/CanvasLayer.js',
      fileName: 'CanvasLayer.js',
      commitId: 'bc0adec0c0ee3daeb7e9f8303185791ba6567077',
      functionName: '',
      functionSignature: '',
      codeType: ''
    },
    {
      id: '454271813878077000',
      code: 'function addLine(marker) {\n    lines.push(marker);\n}',
      repoName: 'huiyan-fe/CanvasLayer',
      startLineNo: 97,
      endLineNo: 99,
      startColNo: 97,
      endColNo: 99,
      language: 'javascript',
      path: 'examples/animationline.js',
      fileName: 'animationline.js',
      commitId: 'bc0adec0c0ee3daeb7e9f8303185791ba6567077',
      functionName: '',
      functionSignature: '',
      codeType: ''
    },
    {
      id: '454271813878077060',
      code: 'function update() {\n    var ctx = this.canvas.getContext("2d");\n\n    if (!ctx) {\n        return;\n    }\n\n    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);\n\n    var temp = {};\n    ctx.fillStyle = "rgba(50, 50, 255, 0.7)";\n    ctx.beginPath();\n    var data = [\n        new BMap.Point(116.297047,39.979542),\n        new BMap.Point(116.321768,39.88748),\n        new BMap.Point(116.494243,39.956539)\n    ];\n\n    for (var i = 0, len = data.length; i < len; i++) {\n        var pixel = map.pointToPixel(data[i]);\n        ctx.fillRect(pixel.x, pixel.y, 30, 30);\n    }\n}',
      repoName: 'huiyan-fe/CanvasLayer',
      startLineNo: 16,
      endLineNo: 38,
      startColNo: 16,
      endColNo: 38,
      language: 'javascript',
      path: 'examples/point.js',
      fileName: 'point.js',
      commitId: 'bc0adec0c0ee3daeb7e9f8303185791ba6567077',
      functionName: '',
      functionSignature: '',
      codeType: ''
    },
    {
      id: '454271813878077000',
      code: 'function Line(options) {\n    this.options = options;\n    this.size = options.size || 15;\n    this.path = options.path;\n    this._index = 0;\n}',
      repoName: 'huiyan-fe/CanvasLayer',
      startLineNo: 45,
      endLineNo: 50,
      startColNo: 45,
      endColNo: 50,
      language: 'javascript',
      path: 'examples/animationline.js',
      fileName: 'animationline.js',
      commitId: 'bc0adec0c0ee3daeb7e9f8303185791ba6567077',
      functionName: '',
      functionSignature: '',
      codeType: ''
    }
  ]
};
