import type { ModuleDispatchProps } from '@fastgpt/global/core/workflow/type/index.d';
import axios from 'axios';
import {
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  WorkflowIOValueTypeEnum
} from '@fastgpt/global/core/workflow/constants';
import {
  DispatchNodeResponseKeyEnum,
  SseResponseEventEnum
} from '@fastgpt/global/core/workflow/runtime/constants';
import { valueTypeFormat } from '../utils';
import { createLogger, perLogger } from '@fastgpt/global/common/util/logger';
import { DispatchNodeResultType } from '@fastgpt/global/core/workflow/runtime/type';
import { getErrText } from '@fastgpt/global/common/error/utils';
import { responseWrite } from '../../../../common/response';
import { textAdaptGptResponse } from '@fastgpt/global/core/workflow/runtime/utils';
import { fetchData, formatHttpError, replaceVariable } from '../httpUtils';
import { installDNSCache } from '@fastgpt/global/common/dns';
import { KError } from '@fastgpt/global/common/error/kwaipilotError';
import { getOpenApiConfig } from '../../../../common/kconf';

installDNSCache();
type PropsArrType = {
  key: string;
  type: string;
  value: string;
};
type KwaipilotToolProps = ModuleDispatchProps<{
  [NodeInputKeyEnum.httpMethod]: string;
  [NodeInputKeyEnum.httpPath]: string;
  [NodeInputKeyEnum.addInputParam]: Record<string, any>;
  [key: string]: any;
}>;
type KwaipilotToolResponse = DispatchNodeResultType<{
  [NodeOutputKeyEnum.failed]?: boolean;
  [key: string]: any;
}>;

const domain =
  process.env.NODE_ENV === 'production'
    ? 'http://openapi-gateway.internal'
    : 'https://is-gateway.corp.kuaishou.com';

const logger = createLogger('node-develop');
export const dispatchDevelop = async (
  props: KwaipilotToolProps
): Promise<KwaipilotToolResponse> => {
  let {
    user,
    emitter,
    detail,
    appId,
    variables,
    node: { outputs },
    histories,
    ksapAuthnToken,
    params: {
      system_httpMethod: httpMethod,
      system_httpPath: httpPath,
      [NodeInputKeyEnum.addInputParam]: dynamicInput,
      ...data
    }
  } = props;
  const concatVariables = {
    appId,
    ...variables,
    histories: histories.slice(-10),
    ...data,
    ...dynamicInput
  };

  const allVariables = {
    [NodeInputKeyEnum.addInputParam]: concatVariables,
    ...concatVariables
  };

  if (!httpPath) {
    logger.error(`url empty, httpReqUrl: ${httpPath}, httpMethod: ${httpMethod}`);
    return Promise.reject('path 为空');
  }

  const httpHeader: PropsArrType[] = [];
  const httpParams: Record<string, any> = {};
  const httpJsonBody: Record<string, any> = {};

  const config = await getOpenApiConfig('kwaipilot');
  // OPENApi 认证
  const accessToken = await getAccessToken(config);
  httpHeader.push({
    key: 'Authorization',
    type: 'string',
    value: `Bearer ${accessToken}`
  });

  // parse header
  const headers = await (() => {
    try {
      if (!httpHeader || httpHeader.length === 0) return {};
      // array
      return httpHeader.reduce((acc: Record<string, string>, item) => {
        const key = replaceVariable(item.key, allVariables);
        const value = replaceVariable(item.value, allVariables);
        acc[key] = valueTypeFormat(value, WorkflowIOValueTypeEnum.string);
        return acc;
      }, {});
    } catch (error) {
      return Promise.reject('core.chat.error.Http header invalid');
    }
  })();

  const entries = Object.entries(data);
  for (const [key, value] of entries) {
    let newValue = replaceVariable(String(value), allVariables);
    newValue = valueTypeFormat(newValue, WorkflowIOValueTypeEnum.string);
    if (key.startsWith(`${NodeInputKeyEnum.httpParams}.`)) {
      const newKey = key.replace(`${NodeInputKeyEnum.httpParams}.`, '');
      // 如果 key 包含 . 则表示是 json 对象
      if (newKey.includes('.')) {
        const [objKey, subKey] = newKey.split('.');
        httpParams[objKey] = {
          ...httpParams[objKey],
          [subKey]: newValue
        };
      } else {
        httpParams[newKey] = newValue;
      }
    } else if (key.startsWith(`${NodeInputKeyEnum.httpJsonBody}.`)) {
      const newKey = key.replace(`${NodeInputKeyEnum.httpJsonBody}.`, '');
      if (newKey.includes('.')) {
        const [objKey, subKey] = newKey.split('.');
        httpJsonBody[objKey] = {
          ...httpJsonBody[objKey],
          [subKey]: newValue
        };
      } else {
        httpJsonBody[newKey] = newValue;
      }
    }
  }

  try {
    const { formatResponse, rawResponse } = await fetchData({
      ksapAuthnToken,
      method: httpMethod,
      url: domain + httpPath,
      headers,
      body: httpJsonBody,
      params: httpParams,
      type: 'tool',
      username: user.username,
      appId
    });

    // format output value type
    const results: Record<string, any> = {};
    for (const key in formatResponse) {
      const output = outputs.find((item) => item.key === key);
      if (!output) continue;
      results[key] = valueTypeFormat(formatResponse[key], output.valueType);
    }

    if (typeof formatResponse[NodeOutputKeyEnum.answerText] === 'string') {
      responseWrite({
        write: (t) => emitter.emit('data', t),
        event: detail ? SseResponseEventEnum.fastAnswer : undefined,
        data: textAdaptGptResponse({
          text: formatResponse[NodeOutputKeyEnum.answerText]
        })
      });
    }

    return {
      [DispatchNodeResponseKeyEnum.nodeResponse]: {
        totalPoints: 0,
        params: Object.keys(httpParams).length > 0 ? httpParams : undefined,
        body: Object.keys(httpJsonBody).length > 0 ? httpJsonBody : undefined,
        headers: Object.keys(headers).length > 0 ? headers : undefined,
        httpResult: rawResponse
      },
      [DispatchNodeResponseKeyEnum.toolResponses]:
        Object.keys(results).length > 0 ? results : rawResponse,
      [NodeOutputKeyEnum.httpRawResponse]: rawResponse,
      ...results
    };
  } catch (error) {
    logger.error('http request error', error);
    return {
      [NodeOutputKeyEnum.failed]: true,
      [DispatchNodeResponseKeyEnum.nodeResponse]: {
        totalPoints: 0,
        params: Object.keys(httpParams).length > 0 ? httpParams : undefined,
        body: Object.keys(httpJsonBody).length > 0 ? httpJsonBody : undefined,
        headers: Object.keys(headers).length > 0 ? headers : undefined,
        httpResult: { error: formatHttpError(error) }
      },
      [NodeOutputKeyEnum.httpRawResponse]: getErrText(error)
    };
  }
};

type Result = {
  accessToken: string;
  refreshToken: string;
  expireAt: number;
};

type RefreshResult = {
  accessToken: string;
  expireTime: number; //有效时间，单位秒
  refreshToken: string;
  appId: string;
};

async function getAccessToken(param: { app_key: string; secret_key: string }): Promise<string> {
  const { app_key, secret_key } = param;

  const { data } = await axios.get<{
    code: number;
    message: string;
    result: RefreshResult;
  }>(`${domain}/token/get`, {
    params: {
      appKey: app_key,
      secretKey: secret_key
    }
  });

  if (data.code) {
    logger.error(`openapi get token error message: ${data.message}`);
    throw new KError('DEVELOP_REQUEST_ERROR', 'engineError', {
      message: `openapi 获取 token 失败: ${data.message}`
    });
  }

  // 小于1分钟，则刷新token
  // const { data } = await axios.get<{
  //   code: number;
  //   message: string;
  //   result: RefreshResult;
  // }>(domain + '/token/refresh', {
  //   params: {
  //     appKey: app_key,
  //     secretKey: secret_key,
  //     refreshToken: result.refreshToken,
  //     grantType: 'refresh_token'
  //   }
  // });

  // if (data.code) {
  //   this.logger.error(`openapi update token error message: ${data.message}`);
  //   throw new Error(`openapi 更新 token 失败: ${data.message}`);
  // }
  if (!data.result?.accessToken) {
    const error = 'openapi 获取 accessToken 为空';
    logger.error(`get accessToken error:${error}, ${JSON.stringify(param)}`);
    throw new KError('DEVELOP_REQUEST_ERROR', 'engineError', {
      error: error
    });
  }

  const { accessToken, expireTime, refreshToken } = data.result;

  return accessToken;
}
