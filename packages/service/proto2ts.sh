#!/usr/bin/env bash

# ⚠️ 注意，这里一定要指定插件路径为 proto-gen-kts
PROTOC_GEN_TS_PATH="./node_modules/.bin/protoc-gen-kts"

GRPC_TOOLS_NODE_PROTOC_PLUGIN="./node_modules/.bin/grpc_tools_node_protoc_plugin"
GRPC_TOOLS_NODE_PROTOC="./node_modules/.bin/grpc_tools_node_protoc"

# proto 存放目录
PROTO_IMPORT_DIR="./common/rpc/llmKwaipilotTokenizer/proto/"
# 代码生成的位置
GEN_OUT_DIR="./common/rpc/llmKwaipilotTokenizer/proto/"

for f in $PROTO_IMPORT_DIR*; do

${GRPC_TOOLS_NODE_PROTOC} \
    --js_out=import_style=commonjs,binary:"${GEN_OUT_DIR}" \
    --grpc_out="grpc_js:${GEN_OUT_DIR}" \
    --plugin=protoc-gen-grpc="${GRPC_TOOLS_NODE_PROTOC_PLUGIN}" \
    -I "${PROTO_IMPORT_DIR}" \
    "${f}"

${GRPC_TOOLS_NODE_PROTOC} \
    --plugin=protoc-gen-ts="${PROTOC_GEN_TS_PATH}" \
    --ts_out="${GEN_OUT_DIR}" \
    -I "${PROTO_IMPORT_DIR}" \
    "${f}"

done
