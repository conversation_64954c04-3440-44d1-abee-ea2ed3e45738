import React from 'react';
import { Flex, Box, FlexProps, Button } from '@chakra-ui/react';
import MyIcon from '../Icon';
import { useTranslation } from 'next-i18next';

type Props = FlexProps & {
  text?: string | React.ReactNode;
  onSubmit?: () => void;
};

const SearchEmpty = ({ text, onSubmit, ...props }: Props) => {
  const { t } = useTranslation();
  return (
    <Flex
      flexDirection={'column'}
      alignItems={'center'}
      justifyContent={'center'}
      {...props}
      lineHeight={'22px'}
    >
      <MyIcon name="common/searchEmpty" w={'68px'} h={'68px'} color={'transparent'} mb={'4px'} />
      <Box color={'#252626'} fontSize={'14px'} fontWeight={'500'} mb={'4px'}>
        无搜索结果
      </Box>
      <Box fontSize={'14px'} color={'#575859'}>
        {text || t('common.empty.Common Tip')}
      </Box>
      <Button onClick={onSubmit} variant={'link'} color={'#326BFB'} fontSize={'14px'}>
        提交反馈
      </Button>
    </Flex>
  );
};

export default SearchEmpty;
