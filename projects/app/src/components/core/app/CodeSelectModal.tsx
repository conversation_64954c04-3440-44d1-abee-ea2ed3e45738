import React, { ChangeEvent, useEffect, useMemo, useState } from 'react';
import {
  Card,
  Flex,
  Box,
  Button,
  ModalBody,
  ModalFooter,
  useTheme,
  Grid,
  Divider,
  Input,
  Avatar,
  Image,
  Tooltip
} from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import type { SelectedCodeBaseType } from '@fastgpt/global/core/workflow/api.d';
import MyTooltip from '@/components/MyTooltip';
import MyIcon from '@fastgpt/web/components/common/Icon';
import { useTranslation } from 'next-i18next';
import DatasetSelectContainer from '@/components/core/dataset/SelectModalV2';
import EmptyTip from '@fastgpt/web/components/common/EmptyTip';
import { debounce } from 'lodash';
import { instance } from '@/web/common/api/kwaipilotRequest';
import { useLoading } from '@fastgpt/web/hooks/useLoading';

interface CodeBaseItem {
  /** 提交分支 */
  commit?: string;
  /**
   * 创建时间
   * @format int64
   */
  createTime?: number;
  /** 仓库描述 */
  description?: string;
  /**
   * 仓库ID
   * @format int64
   */
  gitRepoId?: number;
  /** Icon */
  icon?: string;
  /**
   * ID
   * @format int64
   */
  id?: number;
  /** 仓库地址 - 简洁路径 */
  repoPath?: string;
  /**
   * 状态
   * @format int32
   */
  status?: number;
  /**
   * 更新时间
   * @format int64
   */
  updateTime?: number;
  /** 仓库地址 - 完整路径 */
  url?: string;
}

export const CodeSelectModal = ({
  isOpen,
  defaultSelectedCodeBase = [],
  onChange,
  list,
  onClose
}: {
  isOpen: boolean;
  defaultSelectedCodeBase: SelectedCodeBaseType;
  onChange: (e: SelectedCodeBaseType) => void;
  onClose: () => void;
  list: Array<CodeBaseItem>;
}): JSX.Element => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [searchText, setSearchText] = useState('');

  const allCodeBase = useMemo(() => {
    return list.filter((item) => item.repoPath?.includes(searchText)) || [];
  }, [list, searchText]);

  const [selectedCodeBase, setSelectedCodeBase] =
    useState<SelectedCodeBaseType>(defaultSelectedCodeBase);

  const filterCodeBase = useMemo(() => {
    return {
      selected: allCodeBase.filter((item) =>
        selectedCodeBase.find((dataset) => dataset.repoName === item.repoPath)
      ),
      unSelected: allCodeBase
    };
  }, [allCodeBase, selectedCodeBase]);

  const getRepoName = (repoPath?: string) => {
    if (!repoPath) return '';
    return repoPath.split('/').pop();
  };

  const handleSearch = (e: ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value);
  };

  return (
    <DatasetSelectContainer isOpen={isOpen} tips={t('dataset.Select Code')} onClose={onClose}>
      <Flex h={'100%'} flexDirection={'column'} flex={'1 0 0'}>
        <ModalBody flex={'1 0 0'} overflowY={'auto'} userSelect={'none'}>
          <Input onChange={debounce(handleSearch, 300)} mb={6} placeholder="请输入代码库名称" />
          <Grid
            gridTemplateColumns={[
              'repeat(1, minmax(0, 1fr))',
              'repeat(2, minmax(0, 1fr))',
              'repeat(3, minmax(0, 1fr))'
            ]}
            gridGap={3}
          >
            {filterCodeBase.selected.map((item) =>
              (() => {
                return (
                  <Card
                    key={item.id}
                    p={3}
                    border={theme.borders.base}
                    boxShadow={'sm'}
                    bg={'primary.200'}
                  >
                    <Flex alignItems={'center'} h={'38px'}>
                      <Avatar
                        src={item.icon}
                        w={['24px', '28px']}
                        h={['24px', '28px']}
                        name={getRepoName(item.repoPath)}
                      ></Avatar>
                      <Tooltip label={item.repoPath}>
                        <Box flex={'1 0 0'} w={0} fontSize={'16px'} className="textEllipsis" mx={3}>
                          {item.repoPath}
                        </Box>
                      </Tooltip>
                      <MyIcon
                        name={'delete'}
                        w={'14px'}
                        cursor={'pointer'}
                        _hover={{ color: 'red.500' }}
                        onClick={() => {
                          setSelectedCodeBase((state) =>
                            state.filter((kb) => kb.repoName !== item.repoPath)
                          );
                        }}
                      />
                    </Flex>
                  </Card>
                );
              })()
            )}
          </Grid>

          {filterCodeBase.selected.length > 0 && <Divider my={3} />}

          <Grid
            gridTemplateColumns={[
              'repeat(1, minmax(0, 1fr))',
              'repeat(2, minmax(0, 1fr))',
              'repeat(3, minmax(0, 1fr))'
            ]}
            gridGap={3}
          >
            {filterCodeBase.unSelected.map((item) =>
              (() => {
                return (
                  <MyTooltip key={item.id} label={t('dataset.Select Folder')}>
                    <Card
                      p={3}
                      border={theme.borders.base}
                      boxShadow={'sm'}
                      h={'80px'}
                      cursor={'pointer'}
                      _hover={{
                        boxShadow: 'md'
                      }}
                      onClick={() => {
                        setSelectedCodeBase((state) => [
                          ...state,
                          { commitId: item.commit!, repoName: item.repoPath! }
                        ]);
                      }}
                    >
                      <Flex alignItems={'center'} h={'38px'}>
                        <Avatar
                          src={item.icon}
                          w={['24px', '28px']}
                          h={['24px', '28px']}
                          name={getRepoName(item.repoPath)}
                        ></Avatar>
                        <Tooltip label={item.repoPath}>
                          <Box
                            flex={'1 0 0'}
                            w={0}
                            className="textEllipsis"
                            ml={3}
                            fontSize={'16px'}
                          >
                            {item.repoPath}
                          </Box>
                        </Tooltip>
                      </Flex>
                      {/* <Flex justifyContent={'flex-end'} alignItems={'center'} fontSize={'sm'}>
                        <Box color={'myGray.500'}>被引用{item.refCount}次</Box>
                      </Flex> */}
                    </Card>
                  </MyTooltip>
                );
              })()
            )}
          </Grid>
          {filterCodeBase.unSelected.length === 0 && <EmptyTip text={t('common.folder.empty')} />}
        </ModalBody>

        <ModalFooter>
          <Button
            onClick={() => {
              // filter out the dataset that is not in the kList
              const filterCodeBase = selectedCodeBase.filter((codeBase) => {
                return allCodeBase.find((item) => item.repoPath === codeBase.repoName);
              });

              onClose();
              onChange(filterCodeBase);
            }}
          >
            {t('common.Done')}
          </Button>
        </ModalFooter>
      </Flex>
    </DatasetSelectContainer>
  );
};

export default CodeSelectModal;
