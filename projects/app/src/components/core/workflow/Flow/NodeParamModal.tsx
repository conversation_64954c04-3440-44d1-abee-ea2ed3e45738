import React, { use<PERSON>emo, useCallback, useEffect } from 'react';
import {
  Box,
  Flex,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionIcon,
  AccordionPanel
} from '@chakra-ui/react';
import ClosePanel from './components/ClosePanel';
import Avatar from '@/components/Avatar';
import { FlowNodeItemType } from '@fastgpt/global/core/workflow/type/index.d';

import RenderInput from './nodes/render/RenderInput';
import RenderOutput from './nodes/render/RenderOutput';

import { useTranslation } from 'next-i18next';
import { FlowNodeOutputTypeEnum } from '@fastgpt/global/core/workflow/node/constant';
import { useContextSelector } from 'use-context-selector';
import { WorkflowContext } from './../context';
import { TemplateTypeTheme } from '@fastgpt/global/core/workflow/constants';
import { hexToRgba } from '../utils';
import { ValidationProvider, useValidation } from './nodes/render/RenderInput/ValidationContext';
import { eventBus, EventNameEnum } from '@/web/common/utils/eventbus';
const sliderWidth = 480;

const Content = ({ data, onClose }: { data: FlowNodeItemType; onClose: () => void }) => {
  const { t } = useTranslation();
  const { triggerValidation, resetValidation } = useValidation();
  const nodeId = data?.nodeId;
  const inputs = data?.inputs;
  const outputs = data?.outputs;
  const { avatar, name, intro = '', templateType, flowNodeType } = data;

  const splitToolInputs = useContextSelector(WorkflowContext, (ctx) => ctx.splitToolInputs);
  const { toolInputs, commonInputs } = splitToolInputs(inputs, nodeId);

  const filterHiddenInputs = useMemo(() => commonInputs.filter((item) => true), [commonInputs]);

  useEffect(() => {
    eventBus.on(EventNameEnum.triggerValidation, triggerValidation);
    return () => {
      eventBus.off(EventNameEnum.triggerValidation);
    };
  }, [triggerValidation]);

  useEffect(() => {
    return () => {
      resetValidation();
    };
  }, [resetValidation]);

  const closeIcon = useMemo(() => {
    return (
      <Box
        onClick={onClose}
        position={'absolute'}
        right={'calc(100% - 2px)'}
        top={'50%'}
        transform={'translateY(-50%) rotate(180deg)'}
        cursor={'pointer'}
        color="#898A8C"
        _hover={{
          color: '#000'
        }}
        _before={{
          content: '""',
          position: 'absolute',
          left: '0px',
          top: '50%',
          transform: 'translateY(-50%)',
          height: '86px',
          width: '3px',
          backgroundColor: '#fff'
        }}
      >
        <ClosePanel />
      </Box>
    );
  }, [onClose]);

  const Header = useMemo(() => {
    const themeColor = TemplateTypeTheme[templateType] || '#000';
    return (
      <Box
        position={'relative'}
        padding={'16px'}
        height={'82px'}
        flex={'0 0 auto'}
        borderBottom={'1px solid #EBEDF0'}
        background={`linear-gradient(to bottom, ${hexToRgba(themeColor, 0.1)}, ${hexToRgba(themeColor, 0)})`}
      >
        <Flex alignItems={'center'}>
          <Avatar src={avatar} borderRadius={'0'} objectFit={'contain'} w={'30px'} h={'30px'} />
          <Box marginRight={'auto'} ml={3} fontSize={'16px'} fontWeight={'medium'}>
            {t(name)}
          </Box>
        </Flex>
        <Flex alignItems={'flex-end'} py={1}>
          <Box fontSize={'12px'} color={'#898A8C'} flex={'1 0 0'}>
            {t(intro)}
          </Box>
        </Flex>
      </Box>
    );
  }, [templateType, avatar, t, name, intro]);

  const renderInput = useMemo(() => {
    return (
      <AccordionItem border="none">
        {({ isExpanded }) => (
          <>
            <AccordionButton
              height={'50px'}
              padding={'16px'}
              _expanded={{ bg: 'transparent' }}
              _hover={{ bg: 'transparent' }}
            >
              <AccordionIcon
                transform={isExpanded ? 'rotate(0deg)' : 'rotate(-90deg)'}
                color={'#898A8C'}
                marginRight={'8px'}
              />
              <Box
                as="span"
                flex="1"
                textAlign="left"
                color={'#898A8C'}
                fontSize={'14px'}
                fontWeight={'500'}
              >
                输入
              </Box>
            </AccordionButton>
            <AccordionPanel px={'18px'} pb={4} pt={0} overflow={'visible'}>
              {filterHiddenInputs.length > 0 && (
                <RenderInput mb={7} nodeId={nodeId} flowInputList={commonInputs} />
              )}
            </AccordionPanel>
          </>
        )}
      </AccordionItem>
    );
  }, [commonInputs, filterHiddenInputs.length, nodeId]);

  const renderOutput = useMemo(() => {
    return (
      <AccordionItem border="none">
        {({ isExpanded }) => (
          <>
            <AccordionButton
              _expanded={{ bg: 'transparent' }}
              _hover={{ bg: 'transparent' }}
              _active={{ bg: 'transparent' }}
            >
              <AccordionIcon
                transform={isExpanded ? 'rotate(0deg)' : 'rotate(-90deg)'}
                color={'#898A8C'}
                marginRight={'8px'}
              />
              <Box
                as="span"
                flex="1"
                textAlign="left"
                color={'#898A8C'}
                fontSize={'14px'}
                fontWeight={'500'}
              >
                输出
              </Box>
            </AccordionButton>
            <AccordionPanel px={'18px'} pb={4} pt={0} overflow={'visible'}>
              {outputs.filter((output) => output.type !== FlowNodeOutputTypeEnum.hidden).length >
                0 && <RenderOutput nodeId={nodeId} flowOutputList={outputs} />}
            </AccordionPanel>
          </>
        )}
      </AccordionItem>
    );
  }, [nodeId, outputs]);

  return (
    <>
      {closeIcon}
      {Header}
      <Accordion
        flex={'1 1 auto'}
        overflow={'auto'}
        defaultIndex={[0]}
        allowMultiple
        reduceMotion={true}
      >
        {renderInput}
        {renderOutput}
      </Accordion>
    </>
  );
};

const NodeParamModal = () => {
  const focusNodeId = useContextSelector(WorkflowContext, (v) => v.focusNodeId);
  const nodes = useContextSelector(WorkflowContext, (v) => v.nodes);
  const setFocusNodeId = useContextSelector(WorkflowContext, (v) => v.setFocusNodeId);
  const onCloseParams = useCallback(() => {
    setFocusNodeId(undefined);
  }, [setFocusNodeId]);
  const focusNode = useMemo(() => {
    return nodes.find((node) => node.id === focusNodeId)?.data;
  }, [focusNodeId, nodes]);
  const isOpen = !!focusNode;
  return (
    <>
      <Flex
        zIndex={3}
        flexDirection={'column'}
        position={'absolute'}
        top={'0px'}
        right={0}
        h={'100%'}
        w={isOpen ? ['100%', `${sliderWidth}px`] : '0'}
        bg={'white'}
        boxShadow={'1px 0px 0px 0px #EBEDF0'}
        transition={'.2s ease'}
        userSelect={'none'}
        gap={'12px'}
        overflow={isOpen ? 'none' : 'hidden'}
      >
        {focusNode ? (
          <ValidationProvider>
            <Content data={focusNode} key={focusNodeId} onClose={onCloseParams} />
          </ValidationProvider>
        ) : null}
      </Flex>
    </>
  );
};

export default React.memo(NodeParamModal);
