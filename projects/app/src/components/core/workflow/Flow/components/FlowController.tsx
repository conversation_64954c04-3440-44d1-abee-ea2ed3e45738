import React, { useCallback } from 'react';
import { Background, Panel, ControlButton, MiniMap, useReactFlow, useViewport } from 'reactflow';
import {
  Box,
  Button,
  useDisclosure,
  Collapse,
  Menu,
  MenuItem,
  MenuButton,
  MenuList
} from '@chakra-ui/react';
import { SmallAddIcon, ChevronDownIcon } from '@chakra-ui/icons';

import { checkWorkflowNodeAndConnection } from '@/web/core/workflow/utils';
import { flowNode2StoreNodes } from '@/components/core/workflow/utils';
import { send } from '@/utils/communicate';
import styles from './FlowController.module.scss';

import { maxZoom, minZoom } from '../../constants';

import 'reactflow/dist/style.css';
import { useToast } from '@fastgpt/web/hooks/useToast';
import { useTranslation } from 'next-i18next';
import MyIcon from '@fastgpt/web/components/common/Icon';
import MyTooltip from '@fastgpt/web/components/common/MyTooltip';
import { useContextSelector } from 'use-context-selector';
import { WorkflowContext, getWorkflowStore } from '../../context';

const buttonStyle = {
  border: 'none',
  borderRadius: '6px',
  padding: '7px'
};
enum ZoomType {
  zoomIn = 'zoomIn',
  zoomOut = 'zoomOut',
  zoomToFit = 'zoomToFit',
  zoomTo25 = 'zoomTo25',
  zoomTo50 = 'zoomTo50',
  zoomTo75 = 'zoomTo75',
  zoomTo100 = 'zoomTo100',
  zoomTo200 = 'zoomTo200'
}

const ZOOM_IN_OUT_OPTIONS = [
  {
    key: ZoomType.zoomTo200,
    text: '200%'
  },
  {
    key: ZoomType.zoomTo100,
    text: '100%'
  },
  {
    key: ZoomType.zoomTo75,
    text: '75%'
  },
  {
    key: ZoomType.zoomTo50,
    text: '50%'
  },
  {
    key: ZoomType.zoomTo25,
    text: '25%'
  }
];
const FlowController = React.memo(function FlowController(props: {
  id: number;
  auid: string;
  onCloseTemplate: () => void;
  onOpenTemplate: () => void;
  onExpandAll: () => void;
  onHiddenAll: () => void;
  onAutoLayout: () => void;
}) {
  const { onExpandAll, onHiddenAll, onAutoLayout } = props;
  const { fitView, zoomIn, zoomOut, zoomTo } = useReactFlow();
  const { zoom } = useViewport();
  const edges = useContextSelector(WorkflowContext, (v) => v.edges);
  const onUpdateNodeError = useContextSelector(WorkflowContext, (v) => v.onUpdateNodeError);
  const setFocusNodeId = useContextSelector(WorkflowContext, (v) => v.setFocusNodeId);
  const { toast } = useToast();
  const { t } = useTranslation();
  const { isOpen: isShowMinimap, onToggle: onToggleMinimap } = useDisclosure();

  const flowData2StoreDataAndCheck = useCallback(async () => {
    const { nodes } = await getWorkflowStore();
    const checkResults = checkWorkflowNodeAndConnection({ nodes, edges });
    if (!checkResults) {
      const storeNodes = flowNode2StoreNodes({ nodes, edges });

      return storeNodes;
    } else {
      checkResults.forEach((nodeId) => onUpdateNodeError(nodeId, true));
      toast({
        status: 'warning',
        title: t('core.workflow.Check Failed')
      });
    }
  }, [edges, onUpdateNodeError, t, toast]);

  const onDebug = useCallback(async () => {
    const data = await flowData2StoreDataAndCheck();
    if (data) {
      // 关闭参数modal
      setFocusNodeId(undefined);
      send('start-debug', {
        id: props.id,
        auid: props.auid
      });
    }
  }, [flowData2StoreDataAndCheck, props.auid, props.id, setFocusNodeId]);
  const isMac = !window ? false : window.navigator.userAgent.toLocaleLowerCase().includes('mac');

  const handleZoom = (type: ZoomType) => {
    if (type === ZoomType.zoomTo25) zoomTo(0.25);

    if (type === ZoomType.zoomTo50) zoomTo(0.5);

    if (type === ZoomType.zoomTo75) zoomTo(0.75);

    if (type === ZoomType.zoomTo100) zoomTo(1);

    if (type === ZoomType.zoomTo200) zoomTo(2);
  };

  return (
    <>
      <Collapse in={isShowMinimap}>
        <MiniMap
          nodeColor={'#ABCDFF'}
          nodeBorderRadius={10}
          maskColor="rgba(250, 252, 255, 0.6)"
          maskStrokeColor="#326BFB"
          style={{
            height: 138,
            width: 196,
            padding: '8px',
            left: '50%',
            transform: 'translateX(-60px)',
            marginBottom: 55
          }}
          pannable
        />
      </Collapse>
      <Panel
        position={'bottom-center'}
        style={{
          height: 38,
          display: 'flex',
          alignItems: 'center',
          marginBottom: 5,
          gap: '14px',
          padding: '4px 8px',
          background: 'white',
          borderRadius: '6px',
          boxShadow: '0px 2px 10px 0px rgba(31, 35, 41, 0.1)'
        }}
      >
        <Button
          borderRadius="4px"
          height={'26px'}
          width={'80px'}
          zIndex={1}
          order={-1}
          _hover={{
            background: '#EBF5FF',
            color: '#326BFB'
          }}
          background={'#EBF5FF'}
          color={'#326BFB'}
          fontWeight={'500'}
          onClick={props.onOpenTemplate}
          fontSize={'12px'}
          className={styles.customControlButton}
          leftIcon={<SmallAddIcon style={{ marginRight: '-0.5rem' }} />}
        >
          添加节点
        </Button>
        <Box w="1px" h="12px" bg="#DEE0E3"></Box>
        <Box display={'flex'} gap={'4px'} alignItems={'center'}>
          {/* zoom out */}
          <MyTooltip label="缩小">
            <ControlButton
              onClick={() => zoomOut()}
              style={buttonStyle}
              className={`${styles.customControlButton}`}
              disabled={zoom <= minZoom}
            >
              <MyIcon name={'common/zoomOut'} />
            </ControlButton>
          </MyTooltip>

          <Menu placement="bottom">
            <MenuButton
              as={Button}
              // justifyContent={'space-betwee n'}
              height={'26px'}
              width={'70px'}
              borderRadius={'4px'}
              border="1px"
              padding={0}
              gap={'4px'}
              marginRight={'4px'}
              borderColor="#F0F2F5"
              background={'transparent'}
              color="#252626"
              _hover={{ borderColor: '#487FFF' }}
              rightIcon={<ChevronDownIcon />}
            >
              {parseFloat(`${zoom * 100}`).toFixed(0)}%
            </MenuButton>
            <MenuList>
              {ZOOM_IN_OUT_OPTIONS.map((item) => (
                <MenuItem onClick={() => handleZoom(item.key)} key={item.key}>
                  {item.text}
                </MenuItem>
              ))}
            </MenuList>
          </Menu>
          {/* zoom in */}
          <MyTooltip label="放大">
            <ControlButton
              onClick={() => zoomIn()}
              style={buttonStyle}
              className={`${styles.customControlButton}`}
              disabled={zoom >= maxZoom}
            >
              <MyIcon name={'common/zoomIn'} />
            </ControlButton>
          </MyTooltip>
        </Box>

        <MyTooltip label={isShowMinimap ? '隐藏小地图' : '显示小地图'}>
          <ControlButton
            className={`${styles.customControlButton}`}
            style={{
              background: isShowMinimap ? '#F0F2F5' : '',
              border: 'none'
            }}
            onClick={onToggleMinimap}
          >
            <MyIcon name={'core/modules/minmap'} />
          </ControlButton>
        </MyTooltip>

        <Box w="1px" h="12px" bg="#DEE0E3"></Box>

        <MyTooltip label={'展开全部节点'}>
          <ControlButton style={{ border: 'none' }} onClick={onExpandAll}>
            <MyIcon
              name={'core/modules/expandAll'}
              style={{ width: '16px', height: '16px', maxHeight: '16px', maxWidth: '16px' }}
            />
          </ControlButton>
        </MyTooltip>
        <MyTooltip label={'收起全部节点'}>
          <ControlButton style={{ border: 'none' }} onClick={onHiddenAll}>
            <MyIcon
              name={'core/modules/hiddenAll'}
              style={{ width: '16px', height: '16px', maxHeight: '16px', maxWidth: '16px' }}
            />
          </ControlButton>
        </MyTooltip>
        <MyTooltip label={'整理节点'}>
          <ControlButton style={{ border: 'none' }} onClick={onAutoLayout}>
            <MyIcon
              name={'core/modules/autoLayout'}
              style={{ width: '16px', height: '16px', maxHeight: '16px', maxWidth: '16px' }}
            />
          </ControlButton>
        </MyTooltip>
        <MyTooltip label={'页面居中'}>
          <ControlButton style={{ border: 'none' }} onClick={() => fitView()}>
            <MyIcon
              name={'common/fitView'}
              style={{ width: '16px', height: '16px', maxHeight: '16px', maxWidth: '16px' }}
            />
          </ControlButton>
        </MyTooltip>

        <Button
          position={'absolute'}
          top={'0px'}
          width={'82px'}
          height={'38px'}
          right={'-94px'}
          background={'#30C453'}
          zIndex={1}
          leftIcon={
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M15 8C15 11.866 11.866 15 8 15C4.13401 15 1 11.866 1 8C1 4.13401 4.13401 1 8 1C11.866 1 15 4.13401 15 8ZM13.5 8C13.5 11.0376 11.0376 13.5 8 13.5C4.96243 13.5 2.5 11.0376 2.5 8C2.5 4.96243 4.96243 2.5 8 2.5C11.0376 2.5 13.5 4.96243 13.5 8ZM9.03848 8L7.45001 6.88807V9.11193L9.03848 8ZM7.38809 5.25287C7.14169 5.23677 6.8956 5.28855 6.67658 5.40258C6.45757 5.51661 6.27402 5.68853 6.14592 5.89962C6.01781 6.11071 5.95005 6.35301 5.95001 6.59994V9.40018C5.95005 9.64711 6.01781 9.88929 6.14592 10.1004C6.27402 10.3115 6.45757 10.4834 6.67658 10.5974C6.8956 10.7115 7.14169 10.7632 7.38809 10.7471C7.63448 10.731 7.87184 10.6476 8.07415 10.506L10.0742 9.10595C10.252 8.9815 10.3972 8.81603 10.4975 8.62355C10.5978 8.43104 10.6501 8.21695 10.6502 7.99988C10.6501 7.78282 10.5978 7.56897 10.4975 7.37645C10.3972 7.18397 10.252 7.01843 10.0742 6.89399L8.07406 5.49392C7.87175 5.35235 7.63449 5.26898 7.38809 5.25287Z"
                fill="#ffffff"
              />
            </svg>
          }
          onClick={async (e) => {
            onDebug();
            e.stopPropagation();
          }}
        >
          {t('core.workflow.Debug')}
        </Button>
      </Panel>
      <Background />
    </>
  );
});

export default FlowController;
