import React, { useCallback } from 'react';
import { NodeProps } from 'reactflow';
import NodeCard from '../render/NodeCard';
import { FlowNodeItemType } from '@fastgpt/global/core/workflow/type/index.d';
import { useContextSelector } from 'use-context-selector';
import { WorkflowContext } from '../../../context';
import { send } from '@/utils/communicate';

import { eventBus, EventNameEnum } from '@/web/common/utils/eventbus';
const NodeDevelop = ({
  data,
  selected,
  minW = '350px',
  maxW
}: NodeProps<FlowNodeItemType> & { minW?: string | number; maxW?: string | number }) => {
  const setFocusNodeId = useContextSelector(WorkflowContext, (v) => v.setFocusNodeId);
  const onOpenParamModal = useCallback(
    (isError: boolean) => {
      // 关闭调试窗口
      send('end-debug', null);
      setFocusNodeId(data.nodeId);

      if (isError || data.isError) {
        // 延迟触发校验，避免在节点卡点击时触发校验
        setTimeout(() => {
          eventBus.emit(EventNameEnum.triggerValidation);
        }, 20);
      }
    },
    [data, setFocusNodeId]
  );

  return (
    <NodeCard onClick={onOpenParamModal} minW={minW} maxW={maxW} selected={selected} {...data} />
  );
};
export default React.memo(NodeDevelop);
