import React, { useMemo } from 'react';
import { NodeProps } from 'reactflow';
import NodeCard from './render/NodeCard';
import { FlowNodeItemType } from '@fastgpt/global/core/workflow/type/index.d';
import Container from '../components/Container';
import RenderInput from './render/RenderInput';
import RenderOutput from './render/RenderOutput';
import { useTranslation } from 'next-i18next';
import { FlowNodeOutputTypeEnum } from '@fastgpt/global/core/workflow/node/constant';
import IOTitle from '../components/IOTitle';

const NodeStreamRag = ({
  data,
  selected,
  minW = '350px',
  maxW
}: NodeProps<FlowNodeItemType> & { minW?: string | number; maxW?: string | number }) => {
  const { t } = useTranslation();
  const { nodeId, inputs, outputs } = data;

  const filterHiddenInputs = useMemo(() => inputs.filter((item) => true), [inputs]);

  return (
    <NodeCard minW={minW} maxW={maxW} selected={selected} {...data}>
      {filterHiddenInputs.length > 0 && (
        <>
          <Container>
            <IOTitle text={t('common.Input')} />
            <RenderInput nodeId={nodeId} flowInputList={filterHiddenInputs} />
          </Container>
        </>
      )}
      {outputs.filter((output) => output.type !== FlowNodeOutputTypeEnum.hidden).length > 0 && (
        <>
          <Container>
            <IOTitle text={t('common.Output')} />
            <RenderOutput nodeId={nodeId} flowOutputList={outputs} />
          </Container>
        </>
      )}
    </NodeCard>
  );
};

export default React.memo(NodeStreamRag);
