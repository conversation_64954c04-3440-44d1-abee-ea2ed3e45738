import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

// 校验上下文类型
interface ValidationContextType {
  // 是否触发全局校验
  validateAll: boolean;
  // 触发全局校验的方法
  triggerValidation: () => void;
  // 重置校验状态
  resetValidation: () => void;
  // 记录校验错误的字段
  validationErrors: Record<string, boolean>;
  // 设置字段校验状态
  setFieldError: (fieldKey: string, hasError: boolean | undefined) => void;
  // 是否有校验错误
  hasErrors: boolean;
}

// 创建校验上下文
const ValidationContext = createContext<ValidationContextType>({
  validateAll: false,
  triggerValidation: () => {},
  resetValidation: () => {},
  validationErrors: {},
  setFieldError: () => {},
  hasErrors: false
});

// 校验上下文提供者组件
export const ValidationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // 是否触发全局校验的状态
  const [validateAll, setValidateAll] = useState(false);
  // 记录校验错误的字段
  const [validationErrors, setValidationErrors] = useState<Record<string, boolean>>({});

  // 触发全局校验的方法
  const triggerValidation = useCallback(() => {
    setValidateAll(true);
  }, []);

  // 重置校验状态
  const resetValidation = useCallback(() => {
    setValidateAll(false);
    setValidationErrors({});
  }, []);

  // 设置字段校验状态
  const setFieldError = useCallback((fieldKey: string, hasError: boolean | undefined) => {
    if (hasError === undefined) return; // 忽略 undefined 值

    setValidationErrors((prev) => ({
      ...prev,
      [fieldKey]: hasError
    }));
  }, []);

  // 计算是否有校验错误
  const hasErrors = Object.values(validationErrors).some((error) => error);

  const value = {
    validateAll,
    triggerValidation,
    resetValidation,
    validationErrors,
    setFieldError,
    hasErrors
  };

  return <ValidationContext.Provider value={value}>{children}</ValidationContext.Provider>;
};

// 使用校验上下文的钩子
export const useValidation = () => useContext(ValidationContext);
