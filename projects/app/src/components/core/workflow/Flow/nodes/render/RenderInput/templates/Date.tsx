import React, { useMemo } from 'react';
import type { RenderInputProps } from '../type';
import MySelect from '@fastgpt/web/components/common/MySelect';
import { WorkflowContext } from '@/components/core/workflow/context';
import { useContextSelector } from 'use-context-selector';
import { type DateRange, DayPicker } from 'react-day-picker';
import 'react-day-picker/dist/style.css';
import zhCN from 'date-fns/locale/zh-CN';
import { addDays, format } from 'date-fns';
import { Popover, Input, PopoverTrigger, PopoverContent } from '@chakra-ui/react';
const SelectRender = ({ item, nodeId }: RenderInputProps) => {
  const onChangeNode = useContextSelector(WorkflowContext, (v) => v.onChangeNode);

  const Render = useMemo(() => {
    return (
      <Popover>
        <PopoverTrigger>
          <Input value={item.value} height={'32px'} placeholder="请选择日期" />
        </PopoverTrigger>
        <PopoverContent width={'300px'}>
          <DayPicker
            mode="single"
            locale={zhCN}
            selected={item.value ? new Date(item.value) : undefined}
            onSelect={(e) => {
              onChangeNode({
                nodeId,
                type: 'updateInput',
                key: item.key,
                value: {
                  ...item,
                  value: e ? format(e, 'yyyy-MM-dd') : ''
                }
              });
            }}
          />
        </PopoverContent>
      </Popover>
    );
  }, [item, nodeId, onChangeNode]);

  return Render;
};

export default React.memo(SelectRender);
