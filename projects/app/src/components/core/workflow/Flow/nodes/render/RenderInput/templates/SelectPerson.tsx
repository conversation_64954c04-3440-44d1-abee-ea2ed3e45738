import React, { useCallback, useEffect, useState, useMemo } from 'react';
import type { RenderInputProps } from '../type';
import { Avatar, Box, Flex, FormControl, FormErrorMessage } from '@chakra-ui/react';
import { AsyncSelect, OnChangeValue } from 'chakra-react-select';
import { WorkflowContext } from '@/components/core/workflow/context';
import { useContextSelector } from 'use-context-selector';
import { searchUser } from '@/web/common/api/kwaipilotRequest';
import MyIcon from '@fastgpt/web/components/common/Icon';
import { useValidation } from '../ValidationContext';
import { SearchEmpty } from '@/components/SearchEmpty';

// 定义人员类型
type Person = {
  label: string;
  value: string;
  avatarUrl: string;
};

// 假设这个函数用于获取单个用户信息
const getUserInfo = async (username: string): Promise<Person | null> => {
  if (!username) return null;
  // 搜索用户名完全匹配的用户
  const results = await searchUser(username);
  const person = results.find((user) => user.username === username) || null;
  return person
    ? {
        label: person.name,
        value: person.username,
        avatarUrl: person.avatarUrl
      }
    : null;
};

const SelectPerson = ({ item, nodeId }: RenderInputProps) => {
  const onChangeNode = useContextSelector(WorkflowContext, (v) => v.onChangeNode);

  const [selectedPerson, setSelectedPerson] = useState<Person | null>(null);
  // 获取校验上下文
  const { validateAll } = useValidation();

  // 判断是否显示错误
  const isError = validateAll && item.required && !selectedPerson;

  const loadOptions = useCallback((inputValue: string, callback: (options: Person[]) => void) => {
    searchUser(inputValue).then((results) => {
      callback(
        results?.map((result) => ({
          label: result.name,
          value: result.username,
          avatarUrl: result.avatarUrl
        })) || []
      );
    });
  }, []);

  // 初始化: 如果有默认值，获取用户信息
  useEffect(() => {
    const fetchUserInfo = async () => {
      if (item.value && typeof item.value === 'string') {
        try {
          const user = await getUserInfo(item.value);
          if (user) {
            setSelectedPerson(user);
          }
        } catch (error) {
          console.error('获取用户信息失败:', error);
        }
      }
    };

    fetchUserInfo();
  }, [item.value]);

  // 处理选择用户
  const handleSelectPerson = (person: OnChangeValue<Person, false>) => {
    setSelectedPerson(person);

    // 调用原组件的更新逻辑，只保存username
    onChangeNode({
      nodeId,
      type: 'updateInput',
      key: item.key,
      value: {
        ...item,
        value: person?.value
      }
    });
  };

  const CustomComponents = useMemo(() => {
    return {
      IndicatorSeparator: () => null,
      DropdownIndicator: () => (
        <Flex justifyContent="center" alignItems="center" px="14px">
          <MyIcon
            name="common/backLight"
            w="16px"
            h="16px"
            transform="rotate(270deg)"
            color="#898A8C"
          />
        </Flex>
      )
    };
  }, []);

  return (
    <FormControl isInvalid={isError}>
      <AsyncSelect
        components={CustomComponents}
        value={selectedPerson}
        onChange={handleSelectPerson}
        placeholder="请选择人员"
        loadOptions={loadOptions}
        formatOptionLabel={(option) => (
          <Box>
            <Avatar size="xs" src={option.avatarUrl} mr="2" />
            {option.label}
          </Box>
        )}
        noOptionsMessage={() => <SearchEmpty />}
      />
      {isError && (
        <FormErrorMessage fontSize="sm" color="red.500" mt={1}>
          此字段为必填项
        </FormErrorMessage>
      )}
    </FormControl>
  );
};

export default React.memo(SelectPerson);
