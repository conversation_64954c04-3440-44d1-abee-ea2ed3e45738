import { it, expect, describe } from 'vitest';
import { replaceJsonBodyString } from '@fastgpt/global/core/workflow/runtime/utils';
import type { RuntimeNodeItemType } from '@fastgpt/global/core/workflow/runtime/type';

const variables = {
  cTime: '2025-04-09 21:09:08 Wednesday',
  appId: 'daf752319f384089beb80b9494b983a9',
  userChatInput: '测试',
  username: 'l<PERSON><PERSON><PERSON><PERSON>',
  undefinedVar: undefined,
  nullVar: null,
  objVar: { foo: 'bar' },
  numVar: 123,
  boolVar: true,
  multilineVar: 'line1\nline2\nline3'
};
const runtimeNodes = [
  {
    nodeId: 'userGuide',
    name: '系统配置',
    avatar:
      'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/user-guide.31f7cf5b64fe0649.svg',
    intro: '可以配置应用的系统参数',
    flowNodeType: 'userGuide',
    isEntry: false,
    inputs: [
      {
        key: 'welcomeText',
        renderTypeList: ['hidden'],
        valueType: 'string',
        label: 'core.app.Welcome Text',
        value: '输入日期，如 2024/12/12'
      },
      {
        key: 'variables',
        renderTypeList: ['hidden'],
        valueType: 'any',
        label: 'core.app.Chat Variable',
        value: []
      },
      {
        key: 'questionGuide',
        valueType: 'boolean',
        renderTypeList: ['hidden'],
        label: 'core.app.Question Guide',
        value: false
      },
      {
        key: 'tts',
        renderTypeList: ['hidden'],
        valueType: 'any',
        label: '',
        value: { type: 'web' }
      },
      {
        key: 'whisper',
        renderTypeList: ['hidden'],
        valueType: 'any',
        label: '',
        value: { open: false, autoSend: false, autoTTSResponse: false }
      },
      { key: 'scheduleTrigger', renderTypeList: ['hidden'], valueType: 'any', label: '' },
      {
        key: 'recommendedQuestion',
        valueType: 'arrayString',
        renderTypeList: ['hidden'],
        label: ''
      }
    ],
    outputs: []
  },
  {
    nodeId: '448745',
    name: '流程开始',
    avatar:
      'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/start.73ff1b8785bb8732.svg',
    intro: '',
    flowNodeType: 'workflowStart',
    isEntry: false,
    inputs: [
      {
        key: 'userChatInput',
        renderTypeList: ['reference', 'textarea'],
        valueType: 'string',
        label: '用户问题',
        required: true,
        toolDescription: '用户问题'
      },
      {
        key: 'userChatInputFiles',
        renderTypeList: ['reference', 'textarea'],
        valueType: 'arrayObject',
        label: '用户文件',
        required: true,
        toolDescription: '用户文件'
      }
    ],
    outputs: [
      {
        id: 'userChatInput',
        key: 'userChatInput',
        label: 'core.module.input.label.user question',
        type: 'static',
        valueType: 'string',
        value: '测试'
      },
      {
        id: 'userChatInputFiles',
        key: 'userChatInputFiles',
        label: 'core.module.input.label.user files',
        type: 'static',
        valueType: 'arrayObject',
        value: []
      }
    ]
  },
  {
    nodeId: 'SWp9N23r',
    name: '知识库搜索',
    avatar:
      'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/knowledge.1d45ace5b9ca24b3.svg',
    intro: '从选定知识库中，查询与用户问题相关的内容',
    flowNodeType: 'datasetSearchNodeV2',
    showStatus: true,
    isEntry: false,
    inputs: [
      {
        key: 'datasetsV2',
        renderTypeList: ['selectDatasetV2'],
        label: 'core.module.input.label.Select dataset',
        value: [{ datasetId: 137 }],
        valueType: 'selectDatasetV2',
        list: [],
        required: true
      },
      {
        key: 'topK',
        renderTypeList: ['slider'],
        valueType: 'number',
        label: '最大召回数量',
        description:
          '用于筛选与用户问题相似度最高的片段作为模型回答问题的参考，系统同时会根据选用模型上下文窗口大小动态调整分段数量。',
        value: 7,
        min: 1,
        max: 20,
        step: 1,
        markList: [
          { label: '1', value: 1 },
          { label: '20', value: 20 }
        ],
        required: true
      },
      {
        key: 'threshold',
        renderTypeList: ['slider'],
        valueType: 'number',
        label: '最低相关度',
        description: '用于设置片段筛选的相似度阈值，匹配度低于设定值的内容将不会被检索。',
        value: 0.01,
        min: 0.01,
        max: 0.99,
        step: 0.01,
        markList: [
          { label: '0.01', value: 0.01 },
          { label: '0.99', value: 0.99 }
        ],
        required: true
      },
      {
        key: 'userChatInput',
        renderTypeList: ['reference', 'textarea'],
        valueType: 'string',
        label: '用户问题',
        required: true,
        toolDescription: '需要检索的内容',
        value: ['448745', 'userChatInput']
      }
    ],
    outputs: [
      {
        id: 'quoteQAV2',
        key: 'quoteQAV2',
        label: 'core.module.Dataset quote.label',
        type: 'static',
        valueType: 'arrayString',
        value: ['\n\n# 回答指南\n- 全面分析：we229803*(^(*&$#']
      }
    ]
  },
  {
    nodeId: 'Ql6XQOzz',
    name: 'HTTP 请求',
    avatar:
      'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/HTTP.e231c044ea05668a.svg',
    intro: '发送 HTTP 请求，支持 GET、POST、PUT、DELETE、PATCH 请求',
    flowNodeType: 'httpRequest468',
    showStatus: true,
    isEntry: false,
    inputs: [
      {
        key: 'system_addInputParam',
        renderTypeList: ['addInputParam'],
        valueType: 'dynamic',
        label: '',
        required: false,
        description: 'core.module.input.description.HTTP Dynamic Input',
        editField: { key: true, valueType: true }
      },
      {
        key: 'name',
        valueType: 'string',
        label: 'name',
        renderTypeList: ['reference'],
        description: '',
        canEdit: true,
        editField: { key: true, valueType: true },
        value: ['OPDRd3e7', 'IE9iB6hwl04A']
      },
      {
        key: 'cite',
        valueType: 'arrayString',
        label: 'cite',
        renderTypeList: ['reference'],
        description: '',
        canEdit: true,
        editField: { key: true, valueType: true },
        value: ['SWp9N23r', 'quoteQAV2']
      },
      {
        key: 'system_httpMethod',
        renderTypeList: ['custom'],
        valueType: 'string',
        label: '',
        value: 'POST',
        required: true
      },
      {
        key: 'system_httpTimeout',
        renderTypeList: ['custom'],
        valueType: 'number',
        label: '',
        value: 120,
        min: 5,
        max: 300,
        required: true
      },
      {
        key: 'system_httpReqUrl',
        renderTypeList: ['hidden'],
        valueType: 'string',
        label: '',
        description: 'core.module.input.description.Http Request Url',
        placeholder: 'https://api.ai.com/getInventory',
        required: false,
        value: 'https://kuaishou.com'
      },
      {
        key: 'system_httpHeader',
        renderTypeList: ['custom'],
        valueType: 'any',
        value: [],
        label: '',
        description: 'core.module.input.description.Http Request Header',
        placeholder: 'core.module.input.description.Http Request Header',
        required: false
      },
      {
        key: 'system_httpParams',
        renderTypeList: ['hidden'],
        valueType: 'any',
        value: [],
        label: '',
        required: false
      },
      {
        key: 'system_httpJsonBody',
        renderTypeList: ['hidden'],
        valueType: 'any',
        value: '{\n  "cite": {{cite}},\n  "name": "{{name}}"\n}',
        label: '',
        required: false
      }
    ],
    outputs: [
      {
        id: 'system_addOutputParam',
        key: 'system_addOutputParam',
        type: 'dynamic',
        valueType: 'dynamic',
        label: '',
        editField: { key: true, valueType: true }
      },
      {
        id: 'httpRawResponse',
        key: 'httpRawResponse',
        label: '原始响应',
        description: 'HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。',
        valueType: 'any',
        type: 'static'
      }
    ]
  },
  {
    nodeId: 'TwrldCwI',
    name: '指定回复',
    avatar:
      'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/anwser.067073ed2dbf4740.svg',
    intro: '回复预设的指定内容，适用于引导、提示或标准化问答场景',
    flowNodeType: 'answerNode',
    isEntry: false,
    inputs: [
      {
        key: 'text',
        renderTypeList: ['textarea', 'reference'],
        valueType: 'string',
        label: 'core.module.input.label.Response content',
        description: 'core.module.input.description.Response content',
        placeholder: 'core.module.input.description.Response content'
      }
    ],
    outputs: []
  },
  {
    nodeId: 'OPDRd3e7',
    name: '代码执行器',
    avatar:
      'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/code.ac8266e33dbf190b.svg',
    intro: '根据输入执行 JavaScript 代码，实现自定义逻辑数据处理',
    flowNodeType: 'codeInterpreter',
    isEntry: false,
    inputs: [
      {
        key: 'system_addInputParam',
        renderTypeList: ['addInputParam'],
        valueType: 'dynamic',
        label: '',
        required: false,
        description: 'core.module.input.description.HTTP Dynamic Input',
        editField: { key: true, valueType: true }
      },
      {
        key: 'system_textareaInput',
        renderTypeList: ['JSEditor'],
        valueType: 'string',
        label: '代码',
        description: '通过代码转换数据',
        value:
          "/**\n * input形如 { param1: any, param2: any }\n * 其中 param1 param2 定义在【自定义变量】处\n * 输出必须为对象形式，形如 { result1: any, result2: any }\n * 需要与【自定义输出】处定义保持一致\n */\nfunction main (input) {\n  // put your code here\n  return {\n    username: 'liyuntao'\n  }\n}\n",
        required: true
      }
    ],
    outputs: [
      {
        id: 'system_addOutputParam',
        key: 'system_addOutputParam',
        type: 'dynamic',
        valueType: 'dynamic',
        label: '',
        editField: { key: true, valueType: true }
      },
      {
        id: 'IE9iB6hwl04A',
        type: 'dynamic',
        key: 'username',
        valueType: 'string',
        label: 'username',
        value: 'liyuntao'
      }
    ]
  }
] as RuntimeNodeItemType[];
const allVariables = {
  ...variables,
  system_addInputParam: variables
};

describe('replaceJsonBodyString', () => {
  it('should replace simple variables', () => {
    const input = '{"name": "{{username}}", "time": "{{cTime}}"}';
    const result = replaceJsonBodyString(input, variables, runtimeNodes, allVariables);
    expect(result).toBe('{"name": "liyuntao", "time": "2025-04-09 21:09:08 Wednesday"}');
  });

  it('should handle undefined and null values', () => {
    const input = '{"undefined": {{undefinedVar}}, "null": {{nullVar}}}';
    const result = replaceJsonBodyString(input, variables, runtimeNodes, allVariables);
    expect(result).toBe('{"undefined": null, "null": null}');
  });

  it('should handle object values', () => {
    const input = '{"obj": {{objVar}}}';
    const result = replaceJsonBodyString(input, variables, runtimeNodes, allVariables);
    expect(result).toBe('{"obj": {"foo":"bar"}}');
  });

  it('should handle primitive values', () => {
    const input = '{"num": {{numVar}}, "bool": {{boolVar}}}';
    const result = replaceJsonBodyString(input, variables, runtimeNodes, allVariables);
    expect(result).toBe('{"num": 123, "bool": true}');
  });

  it('should handle variables in quotes', () => {
    const input = '{"quotedStr": "Value is: {{username}}"}';
    const result = replaceJsonBodyString(input, variables, runtimeNodes, allVariables);
    expect(result).toBe('{"quotedStr": "Value is: liyuntao"}');
  });

  it('should handle multiline strings and escape characters', () => {
    const input = '{"multiline": "{{multilineVar}}"}';
    const result = replaceJsonBodyString(input, variables, runtimeNodes, allVariables);
    expect(result).toBe('{"multiline": "line1\\nline2\\nline3"}');
  });

  describe('null and undefined handling', () => {
    it('should convert null variables in quotes to null values', () => {
      const input = '{"property": "{{nullVar}}"}';
      const result = replaceJsonBodyString(input, variables, runtimeNodes, allVariables);
      expect(result).toBe('{"property": null}');

      // 验证解析后的结果
      const parsed = JSON.parse(result);
      expect(parsed.property).toBe(null);
      expect(typeof parsed.property).toBe('object');
    });

    it('should convert undefined variables in quotes to null values', () => {
      const input = '{"property": "{{undefinedVar}}"}';
      const result = replaceJsonBodyString(input, variables, runtimeNodes, allVariables);
      expect(result).toBe('{"property": null}');

      // 验证解析后的结果
      const parsed = JSON.parse(result);
      expect(parsed.property).toBe(null);
      expect(typeof parsed.property).toBe('object');
    });

    it('should convert missing variables in quotes to null values', () => {
      const input = '{"property": "{{missingVar}}"}';
      const result = replaceJsonBodyString(input, variables, runtimeNodes, allVariables);
      expect(result).toBe('{"property": null}');

      // 验证解析后的结果
      const parsed = JSON.parse(result);
      expect(parsed.property).toBe(null);
      expect(typeof parsed.property).toBe('object');
    });

    it('should handle null/undefined variables not in quotes', () => {
      const input = '{"nullProp": {{nullVar}}, "undefinedProp": {{undefinedVar}}}';
      const result = replaceJsonBodyString(input, variables, runtimeNodes, allVariables);
      expect(result).toBe('{"nullProp": null, "undefinedProp": null}');

      // 验证解析后的结果
      const parsed = JSON.parse(result);
      expect(parsed.nullProp).toBe(null);
      expect(parsed.undefinedProp).toBe(null);
    });

    it('should handle arrays with null/undefined variables', () => {
      const input = '{"array": ["{{nullVar}}", {{undefinedVar}}, "{{username}}"]}';
      const result = replaceJsonBodyString(input, variables, runtimeNodes, allVariables);
      expect(result).toBe('{"array": [null, null, "liyuntao"]}');

      // 验证解析后的结果
      const parsed = JSON.parse(result);
      expect(parsed.array[0]).toBe(null);
      expect(parsed.array[1]).toBe(null);
      expect(parsed.array[2]).toBe('liyuntao');
    });

    it('should handle mixed null/undefined and normal variables', () => {
      const input =
        '{"prop1": "{{nullVar}}", "prop2": {{undefinedVar}}, "prop3": "{{username}}", "prop4": {{numVar}}}';
      const result = replaceJsonBodyString(input, variables, runtimeNodes, allVariables);
      expect(result).toBe('{"prop1": null, "prop2": null, "prop3": "liyuntao", "prop4": 123}');

      // 验证解析后的结果
      const parsed = JSON.parse(result);
      expect(parsed.prop1).toBe(null);
      expect(parsed.prop2).toBe(null);
      expect(parsed.prop3).toBe('liyuntao');
      expect(parsed.prop4).toBe(123);
    });

    it('should handle nested objects with null/undefined variables', () => {
      const input = '{"nested": {"inner": "{{nullVar}}"}, "array": [{"item": {{undefinedVar}}}]}';
      const result = replaceJsonBodyString(input, variables, runtimeNodes, allVariables);
      expect(result).toBe('{"nested": {"inner": null}, "array": [{"item": null}]}');

      // 验证解析后的结果
      const parsed = JSON.parse(result);
      expect(parsed.nested.inner).toBe(null);
      expect(parsed.array[0].item).toBe(null);
    });
  });

  describe('string "null" and "undefined" handling', () => {
    const stringNullVariables = {
      ...variables,
      stringNull: 'null',
      stringUndefined: 'undefined',
      normalString: 'hello world'
    };
    const stringNullAllVariables = {
      ...stringNullVariables,
      system_addInputParam: stringNullVariables
    };

    it('should preserve string values that happen to be "null" or "undefined"', () => {
      const input = '{"stringNull": "{{stringNull}}", "stringUndefined": "{{stringUndefined}}"}';
      const result = replaceJsonBodyString(
        input,
        stringNullVariables,
        runtimeNodes,
        stringNullAllVariables
      );
      expect(result).toBe('{"stringNull": "null", "stringUndefined": "undefined"}');

      // 验证解析后的结果
      const parsed = JSON.parse(result);
      expect(parsed.stringNull).toBe('null');
      expect(parsed.stringUndefined).toBe('undefined');
      expect(typeof parsed.stringNull).toBe('string');
      expect(typeof parsed.stringUndefined).toBe('string');
    });

    it('should distinguish between actual null and string "null"', () => {
      const input = '{"actualNull": "{{nullVar}}", "stringNull": "{{stringNull}}"}';
      const result = replaceJsonBodyString(
        input,
        stringNullVariables,
        runtimeNodes,
        stringNullAllVariables
      );
      expect(result).toBe('{"actualNull": null, "stringNull": "null"}');

      // 验证解析后的结果
      const parsed = JSON.parse(result);
      expect(parsed.actualNull).toBe(null);
      expect(parsed.stringNull).toBe('null');
      expect(typeof parsed.actualNull).toBe('object');
      expect(typeof parsed.stringNull).toBe('string');
    });
  });
});
